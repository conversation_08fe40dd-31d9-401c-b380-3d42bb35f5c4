import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject, Subject } from 'rxjs';

/**
 * Interface for serial port information
 */
export interface SerialPortInfo {
  path: string;
  manufacturer?: string;
  serialNumber?: string;
  pnpId?: string;
  locationId?: string;
  productId?: string;
  vendorId?: string;
}

/**
 * Interface for card reader device
 */
export interface CardReaderDevice {
  id: string;
  name: string;
  type: 'magnetic' | 'chip' | 'contactless' | 'hybrid';
  port: SerialPortInfo;
  isConnected: boolean;
  status: 'idle' | 'reading' | 'error' | 'disconnected';
}

/**
 * Interface for card data
 */
export interface CardData {
  track1?: string;
  track2?: string;
  track3?: string;
  cardNumber?: string;
  expiryDate?: string;
  cardholderName?: string;
  serviceCode?: string;
  discretionaryData?: string;
  readMethod: 'magnetic' | 'chip' | 'contactless';
  timestamp: number;
}

/**
 * Interface for device connection options
 */
export interface DeviceConnectionOptions {
  baudRate?: number;
  dataBits?: number;
  stopBits?: number;
  parity?: 'none' | 'even' | 'odd' | 'mark' | 'space';
  flowControl?: boolean;
  timeout?: number;
}

/**
 * Service for managing hardware devices like payment card readers
 */
@Injectable({
  providedIn: 'root'
})
export class HardwareDeviceService {
  private availablePortsSubject = new BehaviorSubject<SerialPortInfo[]>([]);
  private connectedDevicesSubject = new BehaviorSubject<CardReaderDevice[]>([]);
  private cardDataSubject = new Subject<CardData>();
  private deviceStatusSubject = new BehaviorSubject<Map<string, string>>(new Map());
  
  private isElectronApp = false;
  private connectedDevices = new Map<string, CardReaderDevice>();

  constructor() {
    this.isElectronApp = this.checkElectronEnvironment();
    if (this.isElectronApp) {
      this.initializeHardwareSupport();
    }
  }

  /**
   * Check if running in Electron environment
   */
  private checkElectronEnvironment(): boolean {
    return !!(window as any).electronAPI;
  }

  /**
   * Initialize hardware support
   */
  private initializeHardwareSupport(): void {
    if (!this.isElectronApp) return;

    // Refresh available ports on startup
    this.refreshAvailablePorts();
  }

  /**
   * Check if hardware device access is available
   */
  isHardwareAccessAvailable(): boolean {
    return this.isElectronApp;
  }

  /**
   * Get available serial ports
   */
  async refreshAvailablePorts(): Promise<SerialPortInfo[]> {
    if (!this.isElectronApp) return [];

    try {
      const ports = await (window as any).electronAPI.getSerialPorts();
      this.availablePortsSubject.next(ports);
      return ports;
    } catch (error) {
      console.error('Failed to get serial ports:', error);
      return [];
    }
  }

  /**
   * Get available ports observable
   */
  getAvailablePorts(): Observable<SerialPortInfo[]> {
    return this.availablePortsSubject.asObservable();
  }

  /**
   * Get connected devices observable
   */
  getConnectedDevices(): Observable<CardReaderDevice[]> {
    return this.connectedDevicesSubject.asObservable();
  }

  /**
   * Get card data observable
   */
  getCardData(): Observable<CardData> {
    return this.cardDataSubject.asObservable();
  }

  /**
   * Connect to a card reader device
   */
  async connectCardReader(
    port: SerialPortInfo, 
    deviceName: string, 
    deviceType: 'magnetic' | 'chip' | 'contactless' | 'hybrid' = 'magnetic',
    options?: DeviceConnectionOptions
  ): Promise<{ success: boolean; deviceId?: string; error?: string }> {
    if (!this.isElectronApp) {
      return { success: false, error: 'Hardware access not available' };
    }

    try {
      const defaultOptions: DeviceConnectionOptions = {
        baudRate: 9600,
        dataBits: 8,
        stopBits: 1,
        parity: 'none',
        flowControl: false,
        timeout: 5000,
        ...options
      };

      const result = await (window as any).electronAPI.openSerialPort(port.path, defaultOptions);
      
      if (result.success) {
        const deviceId = `device_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const device: CardReaderDevice = {
          id: deviceId,
          name: deviceName,
          type: deviceType,
          port: port,
          isConnected: true,
          status: 'idle'
        };

        this.connectedDevices.set(deviceId, device);
        this.updateConnectedDevicesList();
        
        // Start listening for data from this device
        this.startDeviceDataListener(deviceId, result.portId);

        return { success: true, deviceId };
      } else {
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Failed to connect card reader:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Disconnect a card reader device
   */
  async disconnectCardReader(deviceId: string): Promise<{ success: boolean; error?: string }> {
    if (!this.isElectronApp) {
      return { success: false, error: 'Hardware access not available' };
    }

    const device = this.connectedDevices.get(deviceId);
    if (!device) {
      return { success: false, error: 'Device not found' };
    }

    try {
      const result = await (window as any).electronAPI.closeSerialPort(deviceId);
      
      if (result.success) {
        this.connectedDevices.delete(deviceId);
        this.updateConnectedDevicesList();
        return { success: true };
      } else {
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Failed to disconnect card reader:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Send command to card reader device
   */
  async sendDeviceCommand(deviceId: string, command: string): Promise<{ success: boolean; response?: string; error?: string }> {
    if (!this.isElectronApp) {
      return { success: false, error: 'Hardware access not available' };
    }

    const device = this.connectedDevices.get(deviceId);
    if (!device) {
      return { success: false, error: 'Device not found' };
    }

    try {
      const result = await (window as any).electronAPI.writeToSerialPort(deviceId, command);
      return result;
    } catch (error) {
      console.error('Failed to send device command:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Start listening for data from a device
   */
  private startDeviceDataListener(deviceId: string, portId: string): void {
    // This would typically involve setting up event listeners for incoming data
    // For now, we'll simulate card reading functionality
    console.log(`Started data listener for device ${deviceId} on port ${portId}`);
  }

  /**
   * Process raw card data and emit parsed card information
   */
  private processCardData(deviceId: string, rawData: string): void {
    try {
      // Parse magnetic stripe data (simplified example)
      const cardData = this.parseMagneticStripeData(rawData);
      if (cardData) {
        this.cardDataSubject.next(cardData);
        
        // Update device status
        const device = this.connectedDevices.get(deviceId);
        if (device) {
          device.status = 'idle';
          this.updateConnectedDevicesList();
        }
      }
    } catch (error) {
      console.error('Failed to process card data:', error);
      
      // Update device status to error
      const device = this.connectedDevices.get(deviceId);
      if (device) {
        device.status = 'error';
        this.updateConnectedDevicesList();
      }
    }
  }

  /**
   * Parse magnetic stripe data (simplified implementation)
   */
  private parseMagneticStripeData(rawData: string): CardData | null {
    // This is a simplified parser - real implementation would be more robust
    try {
      // Example format: %B1234567890123445^CARDHOLDER/NAME^2512101?
      const track1Match = rawData.match(/%B(\d+)\^([^?]+)\^(\d{4})(\d{3})?/);
      const track2Match = rawData.match(/;(\d+)=(\d{4})(\d{3})?/);

      if (track1Match || track2Match) {
        const cardData: CardData = {
          readMethod: 'magnetic',
          timestamp: Date.now()
        };

        if (track1Match) {
          cardData.track1 = track1Match[0];
          cardData.cardNumber = track1Match[1];
          cardData.cardholderName = track1Match[2].replace('/', ' ');
          cardData.expiryDate = track1Match[3];
          cardData.serviceCode = track1Match[4];
        }

        if (track2Match) {
          cardData.track2 = track2Match[0];
          if (!cardData.cardNumber) cardData.cardNumber = track2Match[1];
          if (!cardData.expiryDate) cardData.expiryDate = track2Match[2];
          if (!cardData.serviceCode) cardData.serviceCode = track2Match[3];
        }

        return cardData;
      }
    } catch (error) {
      console.error('Failed to parse magnetic stripe data:', error);
    }

    return null;
  }

  /**
   * Update the connected devices list
   */
  private updateConnectedDevicesList(): void {
    const devices = Array.from(this.connectedDevices.values());
    this.connectedDevicesSubject.next(devices);
  }

  /**
   * Get device status
   */
  getDeviceStatus(deviceId: string): string {
    const device = this.connectedDevices.get(deviceId);
    return device ? device.status : 'disconnected';
  }

  /**
   * Check if any devices are connected
   */
  hasConnectedDevices(): boolean {
    return this.connectedDevices.size > 0;
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    // Disconnect all devices
    this.connectedDevices.forEach(async (device) => {
      await this.disconnectCardReader(device.id);
    });
    
    this.connectedDevices.clear();
    this.updateConnectedDevicesList();
  }
}

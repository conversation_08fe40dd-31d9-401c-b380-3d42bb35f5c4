<div class="row">
  <div class="col-12" [ngClass]="{ 'col-md-6 col-lg-5': !isNestedTopUp }">
    <p class="description" [ngClass]="{ withMargin: !isNestedTopUp }">
      <ng-container *ngIf="isNestedTopUp"> There is not enough funds in your wallet. </ng-container>
      Please choose a top up amount to continue.
      <a
        href="https://intercom.help/spriggyschools/en/articles/3202050-how-do-i-top-up-my-account"
        target="_blank"
        >Find out more</a
      >
    </p>
  </div>

  <div class="col-12" [ngClass]="{ 'col-md-6 col-lg-7': !isNestedTopUp }">
    <table class="choicesContainer">
      <tr>
        <td *ngFor="let topUpOption of TOP_UP_AMOUNT_ARRAY; index as i">
          <div
            id="top-up-amount-{{ i }}"
            class="choiceAmount"
            [ngClass]="{ active: IsActive(topUpOption) }"
            (click)="Choose(topUpOption)"
          >
            <p>${{ topUpOption }}</p>
          </div>
        </td>
      </tr>
    </table>
  </div>
</div>

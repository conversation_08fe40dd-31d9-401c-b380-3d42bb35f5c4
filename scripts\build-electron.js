const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * Build script for Electron application
 */
class ElectronBuilder {
  constructor() {
    this.rootDir = path.resolve(__dirname, '..');
    this.distDir = path.join(this.rootDir, 'dist');
    this.electronDistDir = path.join(this.rootDir, 'dist-electron');
  }

  /**
   * Execute command with error handling
   */
  exec(command, options = {}) {
    console.log(`Executing: ${command}`);
    try {
      return execSync(command, { 
        stdio: 'inherit', 
        cwd: this.rootDir,
        ...options 
      });
    } catch (error) {
      console.error(`Command failed: ${command}`);
      console.error(error.message);
      process.exit(1);
    }
  }

  /**
   * Clean previous builds
   */
  clean() {
    console.log('🧹 Cleaning previous builds...');
    
    if (fs.existsSync(this.distDir)) {
      fs.rmSync(this.distDir, { recursive: true, force: true });
    }
    
    if (fs.existsSync(this.electronDistDir)) {
      fs.rmSync(this.electronDistDir, { recursive: true, force: true });
    }
  }

  /**
   * Build Angular application for Electron
   */
  buildAngular() {
    console.log('🔨 Building Angular application for Electron...');
    this.exec('npm run ng build -- --configuration=electron');
  }

  /**
   * Copy Electron files
   */
  copyElectronFiles() {
    console.log('📁 Copying Electron files...');
    
    const electronFiles = [
      'electron-main.js',
      'electron-preload.js'
    ];

    electronFiles.forEach(file => {
      const src = path.join(this.rootDir, file);
      const dest = path.join(this.distDir, 'cashless', file);
      
      if (fs.existsSync(src)) {
        fs.copyFileSync(src, dest);
        console.log(`Copied ${file}`);
      } else {
        console.warn(`Warning: ${file} not found`);
      }
    });
  }

  /**
   * Install production dependencies
   */
  installProductionDeps() {
    console.log('📦 Installing production dependencies...');
    
    // Create a minimal package.json for production
    const productionPackage = {
      name: 'spriggy-cashless-pos',
      version: '1.0.0',
      main: 'electron-main.js',
      dependencies: {
        'electron-is-dev': '^2.0.0',
        'serialport': '^12.0.0'
      }
    };

    const packagePath = path.join(this.distDir, 'cashless', 'package.json');
    fs.writeFileSync(packagePath, JSON.stringify(productionPackage, null, 2));

    // Install dependencies
    this.exec('npm install --production', { 
      cwd: path.join(this.distDir, 'cashless') 
    });
  }

  /**
   * Build Electron application
   */
  buildElectron(platform = 'current') {
    console.log(`🚀 Building Electron application for ${platform}...`);
    
    let buildCommand = 'npm run electron-pack';
    
    switch (platform) {
      case 'win':
      case 'windows':
        buildCommand = 'npm run electron-pack-win';
        break;
      case 'mac':
      case 'macos':
        buildCommand = 'npm run electron-pack-mac';
        break;
      case 'linux':
        buildCommand = 'npm run electron-pack-linux';
        break;
      case 'all':
        buildCommand = 'npm run electron-pack-win && npm run electron-pack-mac && npm run electron-pack-linux';
        break;
    }

    this.exec(buildCommand);
  }

  /**
   * Create installer packages
   */
  createInstallers() {
    console.log('📦 Creating installer packages...');
    
    // This would typically involve additional packaging steps
    // For now, we'll just log the completion
    console.log('Installer packages created in dist-electron directory');
  }

  /**
   * Validate build
   */
  validateBuild() {
    console.log('✅ Validating build...');
    
    const requiredFiles = [
      path.join(this.distDir, 'cashless', 'index.html'),
      path.join(this.distDir, 'cashless', 'electron-main.js'),
      path.join(this.distDir, 'cashless', 'electron-preload.js')
    ];

    let isValid = true;
    requiredFiles.forEach(file => {
      if (!fs.existsSync(file)) {
        console.error(`❌ Missing required file: ${file}`);
        isValid = false;
      }
    });

    if (isValid) {
      console.log('✅ Build validation passed');
    } else {
      console.error('❌ Build validation failed');
      process.exit(1);
    }
  }

  /**
   * Main build process
   */
  async build(options = {}) {
    const { platform = 'current', clean = true, skipInstall = false } = options;
    
    console.log('🚀 Starting Electron build process...');
    console.log(`Platform: ${platform}`);
    console.log(`Clean: ${clean}`);
    
    try {
      if (clean) {
        this.clean();
      }

      this.buildAngular();
      this.copyElectronFiles();
      
      if (!skipInstall) {
        this.installProductionDeps();
      }
      
      this.validateBuild();
      this.buildElectron(platform);
      this.createInstallers();
      
      console.log('🎉 Electron build completed successfully!');
      console.log(`📁 Output directory: ${this.electronDistDir}`);
      
    } catch (error) {
      console.error('❌ Build failed:', error.message);
      process.exit(1);
    }
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const platform = args.find(arg => ['win', 'mac', 'linux', 'all', 'current'].includes(arg)) || 'current';
  const clean = !args.includes('--no-clean');
  const skipInstall = args.includes('--skip-install');

  const builder = new ElectronBuilder();
  builder.build({ platform, clean, skipInstall });
}

module.exports = ElectronBuilder;

const express = require('express');
const path = require('path');
const http = require('http');
const { createProxyMiddleware } = require('http-proxy-middleware');

/**
 * Custom Express server for Electron that properly handles Angular SPA routing
 */
class ElectronServer {
  constructor(port = 4202) {
    this.port = port;
    this.app = express();
    this.server = null;
    this.angularDevServerUrl = 'http://localhost:4201';
  }

  /**
   * Setup middleware and routes
   */
  setupMiddleware() {
    // Enable CORS for Electron
    this.app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
      
      if (req.method === 'OPTIONS') {
        res.sendStatus(200);
      } else {
        next();
      }
    });

    // Proxy API requests to Angular dev server
    this.app.use('/api', createProxyMiddleware({
      target: this.angularDevServerUrl,
      changeOrigin: true,
      logLevel: 'silent'
    }));

    // Proxy static assets (JS, CSS, images, etc.)
    this.app.use('*.js', createProxyMiddleware({
      target: this.angularDevServerUrl,
      changeOrigin: true,
      logLevel: 'silent'
    }));

    this.app.use('*.css', createProxyMiddleware({
      target: this.angularDevServerUrl,
      changeOrigin: true,
      logLevel: 'silent'
    }));

    this.app.use('*.map', createProxyMiddleware({
      target: this.angularDevServerUrl,
      changeOrigin: true,
      logLevel: 'silent'
    }));

    // Proxy webpack dev server specific routes
    this.app.use('/sockjs-node/*', createProxyMiddleware({
      target: this.angularDevServerUrl,
      changeOrigin: true,
      ws: true,
      logLevel: 'silent'
    }));

    // Handle all other routes - serve index.html for SPA routing
    this.app.get('*', (req, res) => {
      console.log(`📄 Serving SPA route: ${req.path}`);

      // Fetch index.html from Angular dev server using http module
      const request = http.get(this.angularDevServerUrl, (response) => {
        if (response.statusCode === 200 || response.statusCode === 404) {
          let html = '';
          response.on('data', (chunk) => {
            html += chunk;
          });

          response.on('end', () => {
            res.send(html);
          });
        } else {
          throw new Error(`Angular server returned ${response.statusCode}`);
        }
      });

      request.on('error', (error) => {
        console.error('❌ Error serving SPA route:', error.message);

        // Fallback HTML
        const fallbackHtml = `
          <!DOCTYPE html>
          <html>
          <head>
            <title>Loading...</title>
            <style>
              body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
              .loading { color: #666; }
              .error { color: #d32f2f; }
            </style>
          </head>
          <body>
            <h1>Angular Application</h1>
            <p class="error">Failed to load from Angular dev server</p>
            <p>Error: ${error.message}</p>
            <button onclick="location.reload()">Retry</button>
          </body>
          </html>
        `;

        res.status(500).send(fallbackHtml);
      });

      request.setTimeout(5000, () => {
        request.destroy();
        res.status(500).send('Request timeout');
      });
    });
  }

  /**
   * Check if Angular dev server is available
   */
  async checkAngularServer() {
    return new Promise((resolve) => {
      const request = http.get(this.angularDevServerUrl, (response) => {
        resolve(response.statusCode >= 200 && response.statusCode < 500);
      });

      request.on('error', () => {
        resolve(false);
      });

      request.setTimeout(3000, () => {
        request.destroy();
        resolve(false);
      });
    });
  }

  /**
   * Start the server
   */
  async start() {
    return new Promise((resolve, reject) => {
      // Check if Angular dev server is available
      this.checkAngularServer().then(isAvailable => {
        if (!isAvailable) {
          console.warn('⚠️ Angular dev server not available, starting anyway...');
        }

        this.setupMiddleware();

        this.server = this.app.listen(this.port, () => {
          console.log(`✅ Electron server running on http://localhost:${this.port}`);
          console.log(`🔗 Proxying to Angular dev server: ${this.angularDevServerUrl}`);
          resolve();
        });

        this.server.on('error', (error) => {
          if (error.code === 'EADDRINUSE') {
            console.error(`❌ Port ${this.port} is already in use`);
          } else {
            console.error('❌ Server error:', error);
          }
          reject(error);
        });
      });
    });
  }

  /**
   * Stop the server
   */
  stop() {
    if (this.server) {
      this.server.close();
      console.log('🛑 Electron server stopped');
    }
  }
}

// If running directly, start the server
if (require.main === module) {
  const server = new ElectronServer();
  
  server.start().catch(error => {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  });

  // Graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    server.stop();
    process.exit(0);
  });
}

module.exports = ElectronServer;

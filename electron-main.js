const { app, BrowserWindow, screen, ipc<PERSON>ain, Menu } = require('electron');
const path = require('path');
const http = require('http');

// Check if we're in development mode
const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged;

// Import custom server
const ElectronServer = require('./electron-server');

// Connection configuration
const CONNECTION_CONFIG = {
  maxAttempts: 30,
  timeout: 3000,
  retryDelay: 1000,
  urls: [
    'http://localhost:4202', // Custom Electron server
    'http://localhost:4201', // Angular dev server
    'http://127.0.0.1:4201',
    'http://localhost:4200',
    'http://127.0.0.1:4200'
  ]
};

// Custom server instance
let electronServer = null;

// Optional dependencies - only load if available
let SerialPort, ReadlineParser;
try {
  SerialPort = require('serialport').SerialPort;
  ReadlineParser = require('@serialport/parser-readline').ReadlineParser;
} catch (error) {
  console.log('Serial port dependencies not available - hardware features disabled');
}

// Keep a global reference of the window objects
let mainWindow;
let studentWindows = new Map();
let serialPorts = new Map(); // Map to store open serial ports

/**
 * Test connection to a specific URL
 */
async function testConnection(url, timeout = 3000) {
  return new Promise((resolve, reject) => {
    const req = http.get(url, (res) => {
      // Accept 200, 404 (Angular SPA), and other 4xx codes as valid connections
      // The important thing is that we get a response from the server
      if (res.statusCode >= 200 && res.statusCode < 500) {
        resolve({ success: true, url, statusCode: res.statusCode });
      } else {
        reject(new Error(`HTTP ${res.statusCode}`));
      }
    });

    req.on('error', (error) => {
      reject(new Error(`Connection failed: ${error.message}`));
    });

    req.setTimeout(timeout, () => {
      req.destroy();
      reject(new Error('Connection timeout'));
    });
  });
}

/**
 * Wait for Angular dev server to be ready with multiple URL attempts
 */
async function waitForAngularServer() {
  console.log('🔍 Testing Angular server connectivity...');

  for (let attempt = 1; attempt <= CONNECTION_CONFIG.maxAttempts; attempt++) {
    console.log(`⏳ Attempt ${attempt}/${CONNECTION_CONFIG.maxAttempts}`);

    // Try each URL in sequence
    for (const url of CONNECTION_CONFIG.urls) {
      try {
        console.log(`  Testing: ${url}`);
        const result = await testConnection(url, CONNECTION_CONFIG.timeout);
        console.log(`✅ Angular server is ready at: ${url}`);
        return { success: true, url: result.url };
      } catch (error) {
        console.log(`  ❌ Failed: ${error.message}`);
      }
    }

    if (attempt < CONNECTION_CONFIG.maxAttempts) {
      console.log(`  ⏳ Waiting ${CONNECTION_CONFIG.retryDelay}ms before next attempt...`);
      await new Promise(resolve => setTimeout(resolve, CONNECTION_CONFIG.retryDelay));
    }
  }

  throw new Error('Angular server not accessible on any URL after maximum attempts');
}

/**
 * Create the main application window
 */
async function createMainWindow() {
  // Get primary display dimensions
  const primaryDisplay = screen.getPrimaryDisplay();
  const { width, height } = primaryDisplay.workAreaSize;

  // Create the browser window
  mainWindow = new BrowserWindow({
    width: Math.min(1200, width - 100),
    height: Math.min(800, height - 100),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'electron-preload.js'),
      webSecurity: false, // Disable for development to avoid CORS issues
      allowRunningInsecureContent: true
    },
    icon: path.join(__dirname, 'src/assets/icons/icon-256x256.png'),
    show: false,
    titleBarStyle: 'default',
    title: 'Spriggy Cashless POS'
  });

  // Determine the URL to load
  let startUrl;
  if (isDev) {
    try {
      // Start custom Electron server
      console.log('🚀 Starting custom Electron server...');
      electronServer = new ElectronServer(4202);
      await electronServer.start();

      // Wait for Angular server to be ready and get working URL
      console.log('⏳ Waiting for Angular server to be ready...');
      const connectionResult = await waitForAngularServer();

      // Use custom Electron server URL
      startUrl = 'http://localhost:4202';
      console.log('🌐 Using custom Electron server for SPA routing');

      // Additional wait to ensure Angular is fully compiled
      console.log('⏳ Waiting for Angular compilation to complete...');
      await new Promise(resolve => setTimeout(resolve, 2000));

    } catch (error) {
      console.error('❌ Failed to setup Electron server:', error.message);
      // Fallback to test page
      startUrl = `file://${path.join(__dirname, 'electron-test.html')}`;
    }
  } else {
    startUrl = `file://${path.join(__dirname, '../dist/cashless/index.html')}`;
  }

  console.log(`🌐 Loading URL: ${startUrl}`);

  // Load the app with error handling
  try {
    console.log('🔄 Attempting to load URL...');
    await mainWindow.loadURL(startUrl);
    console.log('✅ URL loaded successfully');
  } catch (error) {
    console.error('❌ Failed to load URL:', error);
    console.error('Error details:', {
      message: error.message,
      code: error.code,
      stack: error.stack
    });

    // Load error page
    const errorHtml = `
      <html>
        <head><title>Loading Error</title></head>
        <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
          <h1>Failed to Load Application</h1>
          <p>Error: ${error.message}</p>
          <p>Code: ${error.code || 'Unknown'}</p>
          <button onclick="location.reload()" style="padding: 10px 20px; font-size: 16px;">Retry</button>
          <br><br>
          <button onclick="require('electron').shell.openExternal('http://localhost:4200')" style="padding: 10px 20px; font-size: 16px;">Open in Browser</button>
        </body>
      </html>
    `;
    await mainWindow.loadURL(`data:text/html,${encodeURIComponent(errorHtml)}`);
  }

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();

    // Open DevTools in development
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  // Handle navigation errors
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    console.error(`❌ Failed to load ${validatedURL}: ${errorDescription} (${errorCode})`);

    if (isDev && (validatedURL.includes('localhost:4200') || validatedURL.includes('localhost:4201'))) {
      const retryHtml = `
        <html>
          <head><title>Connection Error</title></head>
          <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
            <h1>Cannot Connect to Angular Dev Server</h1>
            <p>Make sure the Angular development server is running:</p>
            <code style="background: #f0f0f0; padding: 10px; display: block; margin: 20px;">npm start</code>
            <button onclick="location.reload()" style="padding: 10px 20px; font-size: 16px; margin: 10px;">Retry</button>
            <button onclick="require('electron').shell.openExternal('http://localhost:4200')" style="padding: 10px 20px; font-size: 16px; margin: 10px;">Open in Browser</button>
          </body>
        </html>
      `;
      mainWindow.loadURL(`data:text/html,${encodeURIComponent(retryHtml)}`);
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
    // Close all student windows when main window closes
    studentWindows.forEach(window => {
      if (!window.isDestroyed()) {
        window.close();
      }
    });
    studentWindows.clear();
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    require('electron').shell.openExternal(url);
    return { action: 'deny' };
  });

  // Add reload functionality
  mainWindow.webContents.on('before-input-event', (event, input) => {
    if (input.control && input.key.toLowerCase() === 'r') {
      mainWindow.reload();
    }
  });
}

/**
 * Create a student view window on specified display
 */
function createStudentWindow(displayId, options = {}) {
  const displays = screen.getAllDisplays();
  const targetDisplay = displays.find(display => display.id.toString() === displayId.toString()) || displays[0];
  
  const defaultOptions = {
    width: 1024,
    height: 768,
    x: targetDisplay.bounds.x + 50,
    y: targetDisplay.bounds.y + 50
  };

  const windowOptions = { ...defaultOptions, ...options };

  const studentWindow = new BrowserWindow({
    width: windowOptions.width,
    height: windowOptions.height,
    x: windowOptions.x,
    y: windowOptions.y,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'electron-preload.js')
    },
    parent: mainWindow,
    modal: false,
    show: false,
    titleBarStyle: 'default',
    title: 'Student View'
  });

  // Load student view URL
  const studentUrl = isDev 
    ? 'http://localhost:4200/#/canteen/pos/tab?viewType=student' 
    : `file://${path.join(__dirname, '../dist/cashless/index.html')}#/canteen/pos/tab?viewType=student`;
  
  studentWindow.loadURL(studentUrl);

  // Show window when ready
  studentWindow.once('ready-to-show', () => {
    studentWindow.show();
  });

  // Store window reference
  const windowId = `student-${Date.now()}`;
  studentWindows.set(windowId, studentWindow);

  // Handle window closed
  studentWindow.on('closed', () => {
    studentWindows.delete(windowId);
    // Notify main window that student window was closed
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('student-window-closed', { windowId, displayId });
    }
  });

  return { windowId, window: studentWindow };
}

/**
 * Get information about all available displays
 */
function getDisplayInfo() {
  const displays = screen.getAllDisplays();
  const primaryDisplay = screen.getPrimaryDisplay();
  
  return displays.map(display => ({
    id: display.id,
    label: display.label || `Display ${display.id}`,
    bounds: display.bounds,
    workArea: display.workArea,
    scaleFactor: display.scaleFactor,
    rotation: display.rotation,
    isPrimary: display.id === primaryDisplay.id
  }));
}

/**
 * Setup IPC handlers for communication with renderer processes
 */
function setupIpcHandlers() {
  // Get display information
  ipcMain.handle('get-displays', () => {
    return getDisplayInfo();
  });

  // Open student window on specific display
  ipcMain.handle('open-student-window', (event, { displayId, options }) => {
    try {
      const result = createStudentWindow(displayId, options);
      return { success: true, windowId: result.windowId };
    } catch (error) {
      console.error('Failed to create student window:', error);
      return { success: false, error: error.message };
    }
  });

  // Close student window
  ipcMain.handle('close-student-window', (event, { windowId }) => {
    const window = studentWindows.get(windowId);
    if (window && !window.isDestroyed()) {
      window.close();
      return { success: true };
    }
    return { success: false, error: 'Window not found' };
  });

  // Close all student windows
  ipcMain.handle('close-all-student-windows', () => {
    let closedCount = 0;
    studentWindows.forEach((window, windowId) => {
      if (!window.isDestroyed()) {
        window.close();
        closedCount++;
      }
    });
    studentWindows.clear();
    return { success: true, closedCount };
  });

  // Get active student windows
  ipcMain.handle('get-active-student-windows', () => {
    const activeWindows = [];
    studentWindows.forEach((window, windowId) => {
      if (!window.isDestroyed()) {
        const bounds = window.getBounds();
        activeWindows.push({
          windowId,
          bounds,
          isVisible: window.isVisible(),
          isFocused: window.isFocused()
        });
      }
    });
    return activeWindows;
  });

  // Hardware device handlers
  setupHardwareDeviceHandlers();
}

/**
 * Setup hardware device IPC handlers
 */
function setupHardwareDeviceHandlers() {
  // Get available serial ports
  ipcMain.handle('get-serial-ports', async () => {
    if (!SerialPort) {
      console.log('SerialPort not available - returning empty list');
      return [];
    }

    try {
      const ports = await SerialPort.list();
      return ports;
    } catch (error) {
      console.error('Failed to list serial ports:', error);
      return [];
    }
  });

  // Open serial port
  ipcMain.handle('open-serial-port', async (_, { path, options }) => {
    if (!SerialPort) {
      return { success: false, error: 'SerialPort not available' };
    }

    try {
      const portId = `port_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      const port = new SerialPort({
        path: path,
        baudRate: options.baudRate || 9600,
        dataBits: options.dataBits || 8,
        stopBits: options.stopBits || 1,
        parity: options.parity || 'none',
        autoOpen: false
      });

      // Setup data parser
      const parser = port.pipe(new ReadlineParser({ delimiter: '\r\n' }));

      // Store port reference
      serialPorts.set(portId, { port, parser });

      // Setup event listeners
      port.on('error', (error) => {
        console.error(`Serial port ${portId} error:`, error);
        if (mainWindow && !mainWindow.isDestroyed()) {
          mainWindow.webContents.send('serial-port-error', { portId, error: error.message });
        }
      });

      parser.on('data', (data) => {
        if (mainWindow && !mainWindow.isDestroyed()) {
          mainWindow.webContents.send('serial-port-data', { portId, data });
        }
      });

      // Open the port
      await new Promise((resolve, reject) => {
        port.open((error) => {
          if (error) {
            reject(error);
          } else {
            resolve();
          }
        });
      });

      return { success: true, portId };
    } catch (error) {
      console.error('Failed to open serial port:', error);
      return { success: false, error: error.message };
    }
  });

  // Close serial port
  ipcMain.handle('close-serial-port', async (_, { portId }) => {
    try {
      const portInfo = serialPorts.get(portId);
      if (!portInfo) {
        return { success: false, error: 'Port not found' };
      }

      await new Promise((resolve, reject) => {
        portInfo.port.close((error) => {
          if (error) {
            reject(error);
          } else {
            resolve();
          }
        });
      });

      serialPorts.delete(portId);
      return { success: true };
    } catch (error) {
      console.error('Failed to close serial port:', error);
      return { success: false, error: error.message };
    }
  });

  // Write to serial port
  ipcMain.handle('write-to-serial-port', async (_, { portId, data }) => {
    try {
      const portInfo = serialPorts.get(portId);
      if (!portInfo) {
        return { success: false, error: 'Port not found' };
      }

      await new Promise((resolve, reject) => {
        portInfo.port.write(data, (error) => {
          if (error) {
            reject(error);
          } else {
            resolve();
          }
        });
      });

      return { success: true };
    } catch (error) {
      console.error('Failed to write to serial port:', error);
      return { success: false, error: error.message };
    }
  });
}

/**
 * Create application menu
 */
function createMenu() {
  const template = [
    {
      label: 'File',
      submenu: [
        {
          label: 'New Student Window',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            const displays = screen.getAllDisplays();
            if (displays.length > 1) {
              createStudentWindow(displays[1].id);
            }
          }
        },
        { type: 'separator' },
        {
          label: 'Exit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' },
        { role: 'close' },
        { type: 'separator' },
        {
          label: 'Close All Student Windows',
          click: () => {
            studentWindows.forEach(window => {
              if (!window.isDestroyed()) {
                window.close();
              }
            });
            studentWindows.clear();
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// App event handlers
app.whenReady().then(async () => {
  await createMainWindow();
  setupIpcHandlers();
  createMenu();

  // Setup display change listeners after app is ready
  screen.on('display-added', () => {
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('displays-changed', getDisplayInfo());
    }
  });

  screen.on('display-removed', () => {
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('displays-changed', getDisplayInfo());
    }
  });

  screen.on('display-metrics-changed', () => {
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('displays-changed', getDisplayInfo());
    }
  });

  app.on('activate', async () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      await createMainWindow();
    }
  });
});

app.on('window-all-closed', () => {
  // Cleanup custom server
  if (electronServer) {
    electronServer.stop();
    electronServer = null;
  }

  if (process.platform !== 'darwin') {
    app.quit();
  }
});

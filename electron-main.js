const { app, BrowserWindow, screen, ipc<PERSON>ain, Menu } = require('electron');
const path = require('path');
const isDev = require('electron-is-dev');

// Keep a global reference of the window objects
let mainWindow;
let studentWindows = new Map();

/**
 * Create the main application window
 */
function createMainWindow() {
  // Get primary display dimensions
  const primaryDisplay = screen.getPrimaryDisplay();
  const { width, height } = primaryDisplay.workAreaSize;

  // Create the browser window
  mainWindow = new BrowserWindow({
    width: Math.min(1200, width - 100),
    height: Math.min(800, height - 100),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'electron-preload.js')
    },
    icon: path.join(__dirname, 'src/assets/icons/icon-256x256.png'),
    show: false,
    titleBarStyle: 'default'
  });

  // Load the app
  const startUrl = isDev 
    ? 'http://localhost:4200' 
    : `file://${path.join(__dirname, '../dist/cashless/index.html')}`;
  
  mainWindow.loadURL(startUrl);

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // Open DevTools in development
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
    // Close all student windows when main window closes
    studentWindows.forEach(window => {
      if (!window.isDestroyed()) {
        window.close();
      }
    });
    studentWindows.clear();
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    require('electron').shell.openExternal(url);
    return { action: 'deny' };
  });
}

/**
 * Create a student view window on specified display
 */
function createStudentWindow(displayId, options = {}) {
  const displays = screen.getAllDisplays();
  const targetDisplay = displays.find(display => display.id.toString() === displayId.toString()) || displays[0];
  
  const defaultOptions = {
    width: 1024,
    height: 768,
    x: targetDisplay.bounds.x + 50,
    y: targetDisplay.bounds.y + 50
  };

  const windowOptions = { ...defaultOptions, ...options };

  const studentWindow = new BrowserWindow({
    width: windowOptions.width,
    height: windowOptions.height,
    x: windowOptions.x,
    y: windowOptions.y,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'electron-preload.js')
    },
    parent: mainWindow,
    modal: false,
    show: false,
    titleBarStyle: 'default',
    title: 'Student View'
  });

  // Load student view URL
  const studentUrl = isDev 
    ? 'http://localhost:4200/#/canteen/pos/tab?viewType=student' 
    : `file://${path.join(__dirname, '../dist/cashless/index.html')}#/canteen/pos/tab?viewType=student`;
  
  studentWindow.loadURL(studentUrl);

  // Show window when ready
  studentWindow.once('ready-to-show', () => {
    studentWindow.show();
  });

  // Store window reference
  const windowId = `student-${Date.now()}`;
  studentWindows.set(windowId, studentWindow);

  // Handle window closed
  studentWindow.on('closed', () => {
    studentWindows.delete(windowId);
    // Notify main window that student window was closed
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('student-window-closed', { windowId, displayId });
    }
  });

  return { windowId, window: studentWindow };
}

/**
 * Get information about all available displays
 */
function getDisplayInfo() {
  const displays = screen.getAllDisplays();
  const primaryDisplay = screen.getPrimaryDisplay();
  
  return displays.map(display => ({
    id: display.id,
    label: display.label || `Display ${display.id}`,
    bounds: display.bounds,
    workArea: display.workArea,
    scaleFactor: display.scaleFactor,
    rotation: display.rotation,
    isPrimary: display.id === primaryDisplay.id
  }));
}

/**
 * Setup IPC handlers for communication with renderer processes
 */
function setupIpcHandlers() {
  // Get display information
  ipcMain.handle('get-displays', () => {
    return getDisplayInfo();
  });

  // Open student window on specific display
  ipcMain.handle('open-student-window', (event, { displayId, options }) => {
    try {
      const result = createStudentWindow(displayId, options);
      return { success: true, windowId: result.windowId };
    } catch (error) {
      console.error('Failed to create student window:', error);
      return { success: false, error: error.message };
    }
  });

  // Close student window
  ipcMain.handle('close-student-window', (event, { windowId }) => {
    const window = studentWindows.get(windowId);
    if (window && !window.isDestroyed()) {
      window.close();
      return { success: true };
    }
    return { success: false, error: 'Window not found' };
  });

  // Close all student windows
  ipcMain.handle('close-all-student-windows', () => {
    let closedCount = 0;
    studentWindows.forEach((window, windowId) => {
      if (!window.isDestroyed()) {
        window.close();
        closedCount++;
      }
    });
    studentWindows.clear();
    return { success: true, closedCount };
  });

  // Get active student windows
  ipcMain.handle('get-active-student-windows', () => {
    const activeWindows = [];
    studentWindows.forEach((window, windowId) => {
      if (!window.isDestroyed()) {
        const bounds = window.getBounds();
        activeWindows.push({
          windowId,
          bounds,
          isVisible: window.isVisible(),
          isFocused: window.isFocused()
        });
      }
    });
    return activeWindows;
  });
}

/**
 * Create application menu
 */
function createMenu() {
  const template = [
    {
      label: 'File',
      submenu: [
        {
          label: 'New Student Window',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            const displays = screen.getAllDisplays();
            if (displays.length > 1) {
              createStudentWindow(displays[1].id);
            }
          }
        },
        { type: 'separator' },
        {
          label: 'Exit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' },
        { role: 'close' },
        { type: 'separator' },
        {
          label: 'Close All Student Windows',
          click: () => {
            studentWindows.forEach(window => {
              if (!window.isDestroyed()) {
                window.close();
              }
            });
            studentWindows.clear();
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// App event handlers
app.whenReady().then(() => {
  createMainWindow();
  setupIpcHandlers();
  createMenu();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createMainWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Handle display changes
screen.on('display-added', () => {
  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.webContents.send('displays-changed', getDisplayInfo());
  }
});

screen.on('display-removed', () => {
  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.webContents.send('displays-changed', getDisplayInfo());
  }
});

screen.on('display-metrics-changed', () => {
  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.webContents.send('displays-changed', getDisplayInfo());
  }
});

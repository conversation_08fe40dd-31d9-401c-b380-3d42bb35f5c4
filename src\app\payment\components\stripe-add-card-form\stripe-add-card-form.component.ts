import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  OnInit,
  Output,
  signal,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';

import { MatButtonModule } from '@angular/material/button';
import {
  SetupIntentResult,
  StripeCardElementChangeEvent,
  StripeCardElementOptions,
  StripeElementsOptions,
} from '@stripe/stripe-js';
import {
  injectStripe,
  StripeCardComponent,
  StripeElementsDirective,
  StripePaymentElementComponent,
} from 'ngx-stripe';
import { BaseComponent, ConfirmModal, TokenResponse } from 'src/app/sharedModels';
import { SchoolsButtonModule } from 'src/app/schools-button/schools-button.module';
import { DialogConfirmComponent } from 'src/app/shared/components';
import { MatDialog } from '@angular/material/dialog';
import { environment } from 'src/environments/environment';
import { SharedModule } from 'src/app/shared/shared.module';
import { PayService } from 'src/app/sharedServices';

@Component({
  selector: 'stripe-add-card-form',
  templateUrl: './stripe-add-card-form.component.html',
  styleUrls: ['./stripe-add-card-form.component.scss'],
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    StripeElementsDirective,
    CommonModule,
    MatButtonModule,
    StripePaymentElementComponent,
    SchoolsButtonModule,
    SharedModule,
    StripeCardComponent,
  ],
})
export class StripeAddCardFormComponent extends BaseComponent implements OnInit {
  @ViewChild(StripePaymentElementComponent) paymentElement: StripePaymentElementComponent;
  @ViewChild(StripeCardComponent) card: StripeCardComponent;
  @Output() exitAddCardForm: EventEmitter<boolean> = new EventEmitter();
  tokenData: TokenResponse;
  tokenLoading = signal<boolean>(true);
  stripeLoading = signal<boolean>(false);
  inValidCardBrand = signal<boolean>(false);
  formComplete = signal<boolean>(false);

  VALID_CARD_BRAND_ARRAY: string[] = ['visa', 'mastercard'];

  readonly stripe = injectStripe(environment.stripePublicKey);

  cardOptions: StripeCardElementOptions = {
    hidePostalCode: true,
  };

  formOptions: StripeElementsOptions = {
    locale: 'en',
  };

  constructor(public dialog: MatDialog, private payService: PayService) {
    super();
  }

  ngOnInit(): void {
    this.getToken();
  }

  getToken(): void {
    this.payService.GetNewToken().subscribe({
      next: (res: TokenResponse) => {
        if (res) {
          this.tokenData = res;
          this.formOptions.clientSecret = this.tokenData.token;
        }
        this.tokenLoading.set(false);
      },
      error: error => {
        this.tokenLoading.set(false);
        this.handleErrorFromService(error);
      },
    });
  }

  cancelClick(): void {
    this.exitAddCardForm.emit();
  }

  onChange(event: StripeCardElementChangeEvent): void {
    this.formComplete.set(event.complete);
    const inValidCardBrand = !this.VALID_CARD_BRAND_ARRAY.includes(event.brand);
    this.inValidCardBrand.set(inValidCardBrand);
  }

  confirmStripeForm(): void {
    if (this.stripeLoading()) return;
    this.stripeLoading.set(true);

    this.stripe
      .confirmCardSetup(this.tokenData.token, {
        payment_method: {
          card: this.card.element,
        },
      })
      .subscribe((result: SetupIntentResult) => {
        this.stripeLoading.set(false);
        this.handleStripeSetupResult(result);
      });
  }

  handleStripeSetupResult(result: SetupIntentResult): void {
    if (result.error) {
      if (result.error?.code === 'incomplete_number') {
        return;
      }
      this.errorPopUp(result.error.message);
      return;
    }
    if (result.setupIntent.status === 'succeeded') {
      this.exitAddCardForm.emit();
    }
  }

  errorPopUp(message: string): void {
    let data = new ConfirmModal();
    data.Title = 'Something went wrong';
    data.Text = message ? message : 'Please try again or contact us for assistance.';
    data.ConfirmButton = 'Ok';

    this.dialog.open(DialogConfirmComponent, {
      width: '500px',
      disableClose: false,
      data: data,
    });
  }
}

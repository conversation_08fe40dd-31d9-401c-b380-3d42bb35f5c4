const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

/**
 * Expose protected methods that allow the renderer process to use
 * the ipcRenderer without exposing the entire object
 */
contextBridge.exposeInMainWorld('electronAPI', {
  // Display management
  getDisplays: () => ipcRenderer.invoke('get-displays'),
  
  // Student window management
  openStudentWindow: (displayId, options) => 
    ipcRenderer.invoke('open-student-window', { displayId, options }),
  
  closeStudentWindow: (windowId) => 
    ipcRenderer.invoke('close-student-window', { windowId }),
  
  closeAllStudentWindows: () => 
    ipcRenderer.invoke('close-all-student-windows'),
  
  getActiveStudentWindows: () => 
    ipcRenderer.invoke('get-active-student-windows'),

  // Event listeners
  onDisplaysChanged: (callback) => {
    ipcRenderer.on('displays-changed', (event, displays) => callback(displays));
  },
  
  onStudentWindowClosed: (callback) => {
    ipcRenderer.on('student-window-closed', (event, data) => callback(data));
  },

  // Remove event listeners
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  },

  // Hardware device access (for future payment card reader integration)
  getSerialPorts: () => ipcRenderer.invoke('get-serial-ports'),
  
  openSerialPort: (path, options) => 
    ipcRenderer.invoke('open-serial-port', { path, options }),
  
  closeSerialPort: (portId) => 
    ipcRenderer.invoke('close-serial-port', { portId }),
  
  writeToSerialPort: (portId, data) => 
    ipcRenderer.invoke('write-to-serial-port', { portId, data }),

  // System information
  getPlatform: () => process.platform,
  getVersion: () => process.versions.electron,
  
  // Check if running in Electron
  isElectron: true
});

/**
 * Expose a minimal Node.js-like environment for specific use cases
 */
contextBridge.exposeInMainWorld('nodeAPI', {
  platform: process.platform,
  versions: process.versions
});

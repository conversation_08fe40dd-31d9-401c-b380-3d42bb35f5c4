<div class="container-fluid pos-container">
  <div class="row justify-content-center">
    <div class="col-12 col-md-12 col-lg-12">
      <!-- Merchant + School Selection Section -->
      <div class="row section mat-elevation-z2 mb-4">
        <div *ngIf="canteenListVisible" class="col-lg-6 col-md-6 col-sm-12">
          <div *ngIf="merchantFormGroup && listCanteens.length > 1" [formGroup]="merchantFormGroup">
            <mat-form-field appearance="outline">
              <mat-label>Merchant</mat-label>
              <mat-select formControlName="canteen" required>
                <mat-option *ngFor="let canteen of listCanteens" [value]="canteen.CanteenId">
                  {{ canteen.CanteenName }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
        <div class="col-lg-6 col-md-6 col-sm-12">
          <div *ngIf="schoolFormGroup" [formGroup]="schoolFormGroup">
            <mat-form-field appearance="outline">
              <mat-label>School</mat-label>
              <mat-select formControlName="school">
                <mat-option *ngFor="let school of this.listSchools" [value]="school.SchoolId">
                  {{ school.Name }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
      </div>

      <!-- Multi-Screen Controls Section -->
      <div *ngIf="isMultiScreenSupported" class="row section mat-elevation-z2 mb-4">
        <div class="col-12">
          <h4 class="section-title">
            <mat-icon>monitor</mat-icon>
            Multi-Screen Display {{ isElectronApp ? '(Electron)' : '(Browser)' }}
          </h4>
          <div class="row">
            <div class="col-lg-6 col-md-6 col-sm-12">
              <!-- Electron Display Selection -->
              <mat-form-field *ngIf="isElectronApp" appearance="outline" class="w-100">
                <mat-label>Select Secondary Display</mat-label>
                <mat-select [(value)]="selectedElectronDisplay" (selectionChange)="onElectronDisplaySelectionChange($event.value)">
                  <mat-option *ngFor="let display of secondaryElectronDisplays" [value]="display">
                    {{ display.label }} ({{ display.bounds.width }}x{{ display.bounds.height }})
                  </mat-option>
                </mat-select>
              </mat-form-field>

              <!-- Browser Screen Selection -->
              <mat-form-field *ngIf="!isElectronApp" appearance="outline" class="w-100">
                <mat-label>Select Secondary Screen</mat-label>
                <mat-select [(value)]="selectedScreen" (selectionChange)="onScreenSelectionChange($event.value)">
                  <mat-option *ngFor="let screen of secondaryScreens" [value]="screen">
                    {{ screen.name }} ({{ screen.width }}x{{ screen.height }})
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            <div class="col-lg-6 col-md-6 col-sm-12 d-flex align-items-end">
              <button
                mat-raised-button
                color="primary"
                class="me-2"
                [disabled]="(!selectedScreen && !selectedElectronDisplay) || studentWindowStatus === 'opening'"
                (click)="openStudentViewOnSecondaryScreen()">
                <mat-icon>open_in_new</mat-icon>
                {{ studentWindowStatus === 'opening' ? 'Opening...' : 'Open Student View' }}
              </button>
              <button
                mat-raised-button
                color="warn"
                [disabled]="!hasOpenStudentWindows"
                (click)="closeStudentWindows()">
                <mat-icon>close</mat-icon>
                Close All
              </button>
            </div>
          </div>
          <div class="row mt-2">
            <div class="col-12">
              <div class="status-indicator" [ngClass]="{
                'status-closed': studentWindowStatus === 'closed',
                'status-opening': studentWindowStatus === 'opening',
                'status-open': studentWindowStatus === 'open',
                'status-error': studentWindowStatus === 'error'
              }">
                <mat-icon>{{ studentWindowStatus === 'open' ? 'check_circle' :
                           studentWindowStatus === 'opening' ? 'hourglass_empty' :
                           studentWindowStatus === 'error' ? 'error' : 'radio_button_unchecked' }}</mat-icon>
                <span>Student View Status: {{ studentWindowStatus | titlecase }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="pos-buttons">
        <div class="row">
          <div class="col-12 col-md-6 mb-3">
            <mat-card class="pos-card merchant-card" (click)="openMerchantView()">
              <mat-card-content class="pos-card-content">
                <mat-icon class="pos-icon">store</mat-icon>
                <h3 class="pos-card-title">Merchant View</h3>
                <p class="pos-card-description">
                  Access the merchant interface for order management and processing
                </p>
              </mat-card-content>
            </mat-card>
          </div>

          <div class="col-12 col-md-6 mb-3">
            <mat-card class="pos-card student-card" (click)="openStudentView()">
              <mat-card-content class="pos-card-content">
                <mat-icon class="pos-icon">school</mat-icon>
                <h3 class="pos-card-title">Student View (New Tab)</h3>
                <p class="pos-card-description">
                  Access the student interface for placing orders and viewing menus in a new browser tab
                </p>
              </mat-card-content>
            </mat-card>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

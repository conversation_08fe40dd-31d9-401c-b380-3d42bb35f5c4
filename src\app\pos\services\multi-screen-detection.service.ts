import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject, fromEvent, merge } from 'rxjs';
import { map, startWith, debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { ScreenInfo } from '../models/pos-messages.interface';

/**
 * Service for detecting and managing multiple screens/displays
 * Uses Screen API and Window.screen properties for cross-browser compatibility
 */
@Injectable({
  providedIn: 'root'
})
export class MultiScreenDetectionService {
  private screensSubject = new BehaviorSubject<ScreenInfo[]>([]);
  private currentScreenSubject = new BehaviorSubject<ScreenInfo | null>(null);
  private isMultiScreenSupportedSubject = new BehaviorSubject<boolean>(false);

  constructor() {
    this.initializeScreenDetection();
    this.setupScreenChangeListeners();
  }

  /**
   * Initialize screen detection capabilities
   */
  private async initializeScreenDetection(): Promise<void> {
    try {
      // Check for modern Screen API support
      if ('getScreenDetails' in window) {
        await this.detectScreensWithModernAPI();
      } else {
        // Fallback to basic screen detection
        this.detectScreensWithFallback();
      }
    } catch (error) {
      console.warn('Screen detection failed, using fallback:', error);
      this.detectScreensWithFallback();
    }
  }

  /**
   * Detect screens using modern Screen API (Chrome 100+)
   */
  private async detectScreensWithModernAPI(): Promise<void> {
    try {
      // Request permission for screen details
      const permission = await navigator.permissions.query({ name: 'window-management' as any });
      
      if (permission.state === 'granted') {
        const screenDetails = await (window as any).getScreenDetails();
        const screens = this.convertToScreenInfo(screenDetails.screens);
        
        this.screensSubject.next(screens);
        this.currentScreenSubject.next(this.getCurrentScreen(screens));
        this.isMultiScreenSupportedSubject.next(screens.length > 1);
        
        // Listen for screen changes
        screenDetails.addEventListener('screenschange', () => {
          this.detectScreensWithModernAPI();
        });
      } else {
        this.detectScreensWithFallback();
      }
    } catch (error) {
      console.warn('Modern screen API failed:', error);
      this.detectScreensWithFallback();
    }
  }

  /**
   * Fallback screen detection using basic Window.screen
   */
  private detectScreensWithFallback(): void {
    const screen = window.screen;
    const screenInfo: ScreenInfo = {
      id: 'primary',
      name: 'Primary Display',
      width: screen.width,
      height: screen.height,
      availWidth: screen.availWidth,
      availHeight: screen.availHeight,
      left: screen.availLeft || 0,
      top: screen.availTop || 0,
      isPrimary: true,
      colorDepth: screen.colorDepth,
      pixelDepth: screen.pixelDepth
    };

    this.screensSubject.next([screenInfo]);
    this.currentScreenSubject.next(screenInfo);
    this.isMultiScreenSupportedSubject.next(false);
  }

  /**
   * Convert native screen objects to ScreenInfo interface
   */
  private convertToScreenInfo(nativeScreens: any[]): ScreenInfo[] {
    return nativeScreens.map((screen, index) => ({
      id: screen.id || `screen-${index}`,
      name: screen.label || `Display ${index + 1}`,
      width: screen.width,
      height: screen.height,
      availWidth: screen.availWidth,
      availHeight: screen.availHeight,
      left: screen.left,
      top: screen.top,
      isPrimary: screen.isPrimary || index === 0,
      colorDepth: screen.colorDepth,
      pixelDepth: screen.pixelDepth
    }));
  }

  /**
   * Get current screen based on window position
   */
  private getCurrentScreen(screens: ScreenInfo[]): ScreenInfo {
    const windowLeft = window.screenX || window.screenLeft || 0;
    const windowTop = window.screenY || window.screenTop || 0;

    // Find screen that contains the current window
    const currentScreen = screens.find(screen => 
      windowLeft >= screen.left && 
      windowLeft < screen.left + screen.width &&
      windowTop >= screen.top && 
      windowTop < screen.top + screen.height
    );

    return currentScreen || screens.find(s => s.isPrimary) || screens[0];
  }

  /**
   * Setup listeners for screen/window changes
   */
  private setupScreenChangeListeners(): void {
    // Listen for window resize and move events
    const windowEvents = merge(
      fromEvent(window, 'resize'),
      fromEvent(window, 'move'),
      fromEvent(window, 'orientationchange')
    );

    windowEvents.pipe(
      debounceTime(250),
      distinctUntilChanged()
    ).subscribe(() => {
      this.refreshScreenDetection();
    });
  }

  /**
   * Refresh screen detection
   */
  private async refreshScreenDetection(): Promise<void> {
    await this.initializeScreenDetection();
  }

  /**
   * Get available screens observable
   */
  getScreens(): Observable<ScreenInfo[]> {
    return this.screensSubject.asObservable();
  }

  /**
   * Get current screen observable
   */
  getCurrentScreen(): Observable<ScreenInfo | null> {
    return this.currentScreenSubject.asObservable();
  }

  /**
   * Check if multi-screen support is available
   */
  isMultiScreenSupported(): Observable<boolean> {
    return this.isMultiScreenSupportedSubject.asObservable();
  }

  /**
   * Get screens synchronously
   */
  getScreensSync(): ScreenInfo[] {
    return this.screensSubject.value;
  }

  /**
   * Get current screen synchronously
   */
  getCurrentScreenSync(): ScreenInfo | null {
    return this.currentScreenSubject.value;
  }

  /**
   * Check if multi-screen is supported synchronously
   */
  isMultiScreenSupportedSync(): boolean {
    return this.isMultiScreenSupportedSubject.value;
  }

  /**
   * Calculate optimal window position for secondary screen
   */
  calculateOptimalPosition(targetScreen: ScreenInfo, windowSize: { width: number; height: number }): { left: number; top: number } {
    const centerX = targetScreen.left + (targetScreen.availWidth - windowSize.width) / 2;
    const centerY = targetScreen.top + (targetScreen.availHeight - windowSize.height) / 2;

    return {
      left: Math.max(targetScreen.left, Math.min(centerX, targetScreen.left + targetScreen.availWidth - windowSize.width)),
      top: Math.max(targetScreen.top, Math.min(centerY, targetScreen.top + targetScreen.availHeight - windowSize.height))
    };
  }

  /**
   * Get secondary screens (non-primary)
   */
  getSecondaryScreens(): ScreenInfo[] {
    return this.screensSubject.value.filter(screen => !screen.isPrimary);
  }

  /**
   * Get primary screen
   */
  getPrimaryScreen(): ScreenInfo | null {
    return this.screensSubject.value.find(screen => screen.isPrimary) || null;
  }

  /**
   * Request permission for window management (if needed)
   */
  async requestWindowManagementPermission(): Promise<boolean> {
    try {
      if ('permissions' in navigator) {
        const permission = await navigator.permissions.query({ name: 'window-management' as any });
        return permission.state === 'granted';
      }
      return false;
    } catch (error) {
      console.warn('Window management permission check failed:', error);
      return false;
    }
  }
}

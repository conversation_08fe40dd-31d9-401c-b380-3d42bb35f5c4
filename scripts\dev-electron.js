const { spawn } = require('child_process');
const { createServer } = require('http');
const path = require('path');

/**
 * Development script for Electron application
 */
class ElectronDev {
  constructor() {
    this.rootDir = path.resolve(__dirname, '..');
    this.angularProcess = null;
    this.electronProcess = null;
    this.isShuttingDown = false;
  }

  /**
   * Wait for Angular dev server to be ready
   */
  async waitForAngular(port = 4200, timeout = 60000) {
    console.log(`⏳ Waiting for Angular dev server on port ${port}...`);

    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      try {
        await new Promise((resolve, reject) => {
          const testReq = require('http').get(`http://localhost:${port}`, (res) => {
            if (res.statusCode === 200) {
              resolve(res);
            } else {
              reject(new Error(`HTTP ${res.statusCode}`));
            }
          });

          testReq.on('error', reject);
          testReq.setTimeout(2000, () => {
            testReq.destroy();
            reject(new Error('Timeout'));
          });
        });

        console.log('✅ Angular dev server is ready!');

        // Additional wait to ensure Angular is fully compiled
        console.log('⏳ Waiting for Angular compilation to complete...');
        await new Promise(resolve => setTimeout(resolve, 3000));

        return true;
      } catch (error) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    throw new Error('Angular dev server failed to start within timeout');
  }

  /**
   * Start Angular development server
   */
  startAngular() {
    console.log('🅰️ Starting Angular development server...');
    
    this.angularProcess = spawn('npm', ['start'], {
      cwd: this.rootDir,
      stdio: 'pipe',
      shell: true
    });

    this.angularProcess.stdout.on('data', (data) => {
      const output = data.toString();
      console.log(`[Angular] ${output.trim()}`);
    });

    this.angularProcess.stderr.on('data', (data) => {
      const output = data.toString();
      if (!output.includes('Warning')) {
        console.error(`[Angular Error] ${output.trim()}`);
      }
    });

    this.angularProcess.on('close', (code) => {
      if (!this.isShuttingDown) {
        console.log(`Angular process exited with code ${code}`);
      }
    });

    this.angularProcess.on('error', (error) => {
      console.error('Failed to start Angular process:', error);
    });
  }

  /**
   * Start Electron application
   */
  startElectron() {
    console.log('⚡ Starting Electron application...');

    // Check if electron is available
    try {
      require.resolve('electron');
    } catch (error) {
      console.error('❌ Electron is not installed. Please install it first:');
      console.error('npm install --save-dev electron electron-builder electron-is-dev');
      return;
    }

    this.electronProcess = spawn('npm', ['run', 'electron'], {
      cwd: this.rootDir,
      stdio: 'pipe',
      shell: true,
      env: {
        ...process.env,
        NODE_ENV: 'development'
      }
    });

    this.electronProcess.stdout.on('data', (data) => {
      const output = data.toString();
      console.log(`[Electron] ${output.trim()}`);
    });

    this.electronProcess.stderr.on('data', (data) => {
      const output = data.toString();
      console.error(`[Electron Error] ${output.trim()}`);
    });

    this.electronProcess.on('close', (code) => {
      if (!this.isShuttingDown) {
        console.log(`Electron process exited with code ${code}`);
        this.shutdown();
      }
    });

    this.electronProcess.on('error', (error) => {
      console.error('Failed to start Electron process:', error);
    });
  }

  /**
   * Setup graceful shutdown
   */
  setupShutdown() {
    const shutdown = () => {
      if (this.isShuttingDown) return;
      this.shutdown();
    };

    process.on('SIGINT', shutdown);
    process.on('SIGTERM', shutdown);
    process.on('exit', shutdown);
  }

  /**
   * Shutdown all processes
   */
  shutdown() {
    if (this.isShuttingDown) return;
    
    console.log('🛑 Shutting down development environment...');
    this.isShuttingDown = true;

    if (this.electronProcess && !this.electronProcess.killed) {
      console.log('Stopping Electron...');
      this.electronProcess.kill('SIGTERM');
    }

    if (this.angularProcess && !this.angularProcess.killed) {
      console.log('Stopping Angular dev server...');
      this.angularProcess.kill('SIGTERM');
    }

    setTimeout(() => {
      process.exit(0);
    }, 2000);
  }

  /**
   * Check if Angular is already running
   */
  async checkAngularRunning(port = 4200) {
    try {
      await new Promise((resolve, reject) => {
        const testReq = require('http').get(`http://localhost:${port}`, (res) => {
          if (res.statusCode === 200) {
            resolve(res);
          } else {
            reject(new Error(`HTTP ${res.statusCode}`));
          }
        });

        testReq.on('error', reject);
        testReq.setTimeout(2000, () => {
          testReq.destroy();
          reject(new Error('Timeout'));
        });
      });

      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Start development environment
   */
  async start() {
    console.log('🚀 Starting Electron development environment...');

    try {
      this.setupShutdown();

      // Check if Angular is already running
      const isAngularRunning = await this.checkAngularRunning();

      if (isAngularRunning) {
        console.log('✅ Angular dev server is already running on port 4200');
      } else {
        console.log('🅰️ Starting Angular development server...');
        this.startAngular();

        // Wait for Angular to be ready
        await this.waitForAngular();
      }

      // Start Electron
      this.startElectron();

      console.log('✅ Development environment started successfully!');
      console.log('📝 Tips:');
      console.log('  - Angular dev server: http://localhost:4200');
      console.log('  - Electron app will open automatically');
      console.log('  - Press Ctrl+C to stop both processes');

    } catch (error) {
      console.error('❌ Failed to start development environment:', error.message);
      this.shutdown();
      process.exit(1);
    }
  }

  /**
   * Restart Electron only (useful during development)
   */
  async restartElectron() {
    console.log('🔄 Restarting Electron...');
    
    if (this.electronProcess && !this.electronProcess.killed) {
      this.electronProcess.kill('SIGTERM');
      
      // Wait a bit for the process to close
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    this.startElectron();
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const command = args[0] || 'start';
  
  const dev = new ElectronDev();
  
  switch (command) {
    case 'start':
      dev.start();
      break;
    case 'restart-electron':
      dev.restartElectron();
      break;
    default:
      console.log('Usage: node dev-electron.js [start|restart-electron]');
      process.exit(1);
  }
}

module.exports = ElectronDev;

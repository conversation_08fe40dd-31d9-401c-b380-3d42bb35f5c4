const { app, BrowserWindow } = require('electron');
const path = require('path');

// Simple test to verify Electron can load content
app.whenReady().then(() => {
  const testWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: false
    },
    show: true
  });

  console.log('🔍 Testing Electron content loading...');
  
  // Test loading Angular app
  testWindow.loadURL('http://localhost:4201').then(() => {
    console.log('✅ Successfully loaded Angular app');
    
    // Check if content is loaded after a delay
    setTimeout(() => {
      testWindow.webContents.executeJavaScript(`
        document.title || 'No title'
      `).then(title => {
        console.log('📄 Page title:', title);
      });
      
      testWindow.webContents.executeJavaScript(`
        document.body ? document.body.innerHTML.length : 0
      `).then(contentLength => {
        console.log('📏 Content length:', contentLength, 'characters');
        if (contentLength > 100) {
          console.log('✅ Content appears to be loaded');
        } else {
          console.log('⚠️ Content may not be fully loaded');
        }
      });
      
      testWindow.webContents.executeJavaScript(`
        Array.from(document.querySelectorAll('*')).length
      `).then(elementCount => {
        console.log('🏗️ DOM elements:', elementCount);
      });
      
    }, 3000);
    
  }).catch(error => {
    console.error('❌ Failed to load Angular app:', error);
  });

  testWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    console.error('❌ Failed to load:', validatedURL, errorDescription);
  });

  testWindow.webContents.on('dom-ready', () => {
    console.log('✅ DOM is ready');
  });

  testWindow.webContents.on('did-finish-load', () => {
    console.log('✅ Page finished loading');
  });
});

app.on('window-all-closed', () => {
  app.quit();
});

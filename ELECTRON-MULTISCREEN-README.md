# Spriggy Cashless POS - Multi-Screen & Electron Integration

This document describes the multi-screen display functionality and Electron integration for the Spriggy Cashless POS application.

## Overview

The application now supports:
1. **Multi-screen display functionality** - Open student views on secondary displays
2. **Electron desktop application** - Native desktop capabilities with hardware device access
3. **Hardware device integration** - Support for payment card readers and other POS hardware

## Features

### Multi-Screen Display
- **Browser-based multi-screen detection** using Screen API and Window.screen properties
- **Electron-based multi-screen management** with native display APIs
- **Automatic screen detection** and optimal window positioning
- **Cross-tab communication** for synchronized merchant and student views
- **Fallback support** for browsers without advanced screen APIs

### Electron Integration
- **Native desktop application** with proper window management
- **Hardware device access** through Node.js APIs
- **Serial port communication** for payment card readers
- **Multi-window management** with proper screen positioning
- **Auto-updater support** (ready for implementation)

### Hardware Device Support
- **Payment card reader integration** via serial ports
- **Magnetic stripe card reading** with data parsing
- **Device connection management** with status monitoring
- **Error handling and recovery** for device communication

## Architecture

### Services

#### Multi-Screen Services
- `MultiScreenDetectionService` - Browser-based screen detection
- `StudentWindowManagerService` - Browser window management
- `ElectronService` - Electron API integration
- `ElectronWindowManagerService` - Electron window management
- `HardwareDeviceService` - Hardware device integration

#### Communication
- `PosCommunicationService` - Enhanced with multi-screen message types
- Cross-tab messaging using BroadcastChannel API with localStorage fallback
- IPC communication between Electron main and renderer processes

### Components
- Enhanced `PosComponent` with multi-screen UI controls
- Automatic detection of Electron vs browser environment
- Responsive UI that adapts to available display capabilities

## Installation & Setup

### Prerequisites
```bash
# Install Electron dependencies (when available)
npm install --save-dev electron electron-builder electron-is-dev
npm install --save-dev concurrently wait-on

# Install hardware device dependencies
npm install serialport @serialport/parser-readline
```

### Development

#### Browser Development (Standard)
```bash
npm start
# Navigate to http://localhost:4200
```

#### Electron Development
```bash
npm run electron-dev
# Starts both Angular dev server and Electron app
```

### Building

#### Browser Build (Standard)
```bash
npm run build
```

#### Electron Build
```bash
# Build for current platform
npm run electron-build

# Build for specific platforms
npm run electron-build-win
npm run electron-build-mac
npm run electron-build-linux

# Build for all platforms
npm run electron-build-all
```

## Usage

### Multi-Screen Setup (Browser)

1. **Connect secondary display** to your computer
2. **Open the POS application** in your browser
3. **Navigate to the main POS interface**
4. **Multi-screen controls will appear** if multiple displays are detected
5. **Select target display** from the dropdown
6. **Click "Open Student View"** to launch on secondary screen

### Multi-Screen Setup (Electron)

1. **Launch the Electron application**
2. **Connect secondary display** (displays are auto-detected)
3. **Use the enhanced multi-screen controls** in the main window
4. **Select target display** and click "Open Student View"
5. **Windows are automatically positioned** optimally on the selected display

### Hardware Device Integration

#### Payment Card Reader Setup
1. **Connect card reader** via USB/Serial
2. **Open hardware device settings** in the application
3. **Select the appropriate serial port**
4. **Configure device settings** (baud rate, etc.)
5. **Test card reading functionality**

#### Supported Devices
- **Magnetic stripe readers** (MSR)
- **Chip card readers** (EMV) - Ready for implementation
- **Contactless readers** (NFC) - Ready for implementation
- **Custom serial devices** via configurable protocols

## Configuration

### Environment Variables
```typescript
// src/environments/environment.electron.ts
export const environment = {
  production: true,
  isElectron: true,
  electron: {
    enableDevTools: false,
    enableHardwareAcceleration: true,
    multiScreenSupported: true,
    hardwareDeviceAccess: true
  }
};
```

### Window Management Preferences
```typescript
interface WindowManagementPreferences {
  autoOpenStudentView: boolean;
  preferredStudentDisplay?: number;
  studentWindowSize: { width: number; height: number };
  rememberWindowPositions: boolean;
  enableHardwareAcceleration: boolean;
}
```

## API Reference

### Multi-Screen Detection
```typescript
// Get available screens
const screens = await multiScreenService.getScreens();

// Check multi-screen support
const isSupported = multiScreenService.isMultiScreenSupportedSync();

// Open student window on specific screen
await studentWindowManager.openStudentWindow({
  width: 1024,
  height: 768,
  targetScreen: selectedScreen
});
```

### Electron Integration
```typescript
// Check if running in Electron
const isElectron = electronService.isElectron();

// Get Electron displays
const displays = await electronService.getDisplays();

// Open student window on Electron display
await electronService.openStudentWindow(displayId, {
  width: 1024,
  height: 768,
  x: 100,
  y: 100
});
```

### Hardware Device Access
```typescript
// Get available serial ports
const ports = await hardwareService.refreshAvailablePorts();

// Connect card reader
const result = await hardwareService.connectCardReader(
  port, 
  'Card Reader', 
  'magnetic'
);

// Listen for card data
hardwareService.getCardData().subscribe(cardData => {
  console.log('Card read:', cardData);
});
```

## Troubleshooting

### Multi-Screen Issues
- **No secondary screens detected**: Check display connections and refresh the page
- **Window positioning issues**: Verify display arrangement in OS settings
- **Cross-tab communication fails**: Check browser permissions and localStorage access

### Electron Issues
- **App won't start**: Check Node.js version compatibility
- **Hardware access denied**: Verify application permissions
- **Build failures**: Ensure all dependencies are installed

### Hardware Device Issues
- **Device not detected**: Check USB/Serial connections and drivers
- **Communication errors**: Verify baud rate and port settings
- **Card reading fails**: Check device compatibility and configuration

## Security Considerations

### Browser Environment
- **Screen API permissions** may be required in some browsers
- **Cross-origin restrictions** apply to multi-window communication
- **localStorage access** required for cross-tab messaging

### Electron Environment
- **Node.js integration** is disabled in renderer processes for security
- **Context isolation** is enabled with secure preload scripts
- **Hardware device access** is controlled through main process

## Future Enhancements

### Planned Features
- **Touch screen support** for student displays
- **Kiosk mode** for dedicated POS terminals
- **Advanced hardware integration** (printers, cash drawers)
- **Auto-updater implementation** for Electron builds
- **Performance monitoring** and analytics

### Hardware Expansion
- **Barcode scanners** integration
- **Receipt printers** support
- **Cash drawer** control
- **Scale integration** for weight-based items

## Support

For technical support or questions about the multi-screen and Electron integration:

1. Check the troubleshooting section above
2. Review the browser console for error messages
3. Verify hardware device connections and drivers
4. Contact the development team with specific error details

## License

This implementation is part of the Spriggy Cashless POS system and follows the same licensing terms as the main application.

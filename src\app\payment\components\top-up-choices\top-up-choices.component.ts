import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'payment-top-up-choices',
  templateUrl: './top-up-choices.component.html',
  styleUrls: ['./top-up-choices.component.scss'],
})
export class TopUpChoicesComponent implements OnInit {
  @Input() isNestedTopUp: boolean;
  @Input() chargeAmount: number = null;
  @Output() choiceChanged: EventEmitter<number> = new EventEmitter();
  TOP_UP_AMOUNT_ARRAY = [10, 20, 40, 60];
  TOP_UP_INTERVAL = 10;
  private _currentChoice: number;

  constructor() {}

  ngOnInit(): void {
    if (this.chargeAmount <= 0) {
      return;
    }
    while (this.chargeAmount > this.TOP_UP_AMOUNT_ARRAY[0]) {
      this.TOP_UP_AMOUNT_ARRAY = this.TOP_UP_AMOUNT_ARRAY.map(amount => amount + this.TOP_UP_INTERVAL);
    }
  }

  Choose(newChoice: number): void {
    this._currentChoice = newChoice;
    this.choiceChanged.next(this._currentChoice);
  }

  IsActive(amount: number): boolean {
    return this._currentChoice == amount;
  }
}

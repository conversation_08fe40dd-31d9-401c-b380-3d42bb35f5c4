<ng-container *ngIf="tokenLoading(); else showForm">
  <div class="col-12 d-flex justify-content-center align-items-center p-2">
    <app-spinner [manual]="true"></app-spinner>
  </div>
</ng-container>

<ng-template #showForm>
  <div *ngIf="tokenData?.token; else noForm" class="mt-4 mb-4">
    <div class="input-container p-3">
      <ngx-stripe-elements [stripe]="stripe" [elementsOptions]="formOptions">
        <ngx-stripe-card
          [options]="cardOptions"
          (change)="onChange($event)"
          id="stripe-card-details-form"
        ></ngx-stripe-card>
      </ngx-stripe-elements>
    </div>
    <p *ngIf="inValidCardBrand()" class="error">Only Visa or Mastercard are accepted</p>

    <div class="mt-4">
      <primary-button
        id="stripe-connect-button"
        text="Connect"
        (onPress)="confirmStripeForm()"
        [loading]="stripeLoading()"
        [disabled]="!formComplete() || inValidCardBrand()"
      >
      </primary-button>
    </div>
    <button
      mat-flat-button
      class="SecondaryButton"
      type="button"
      (click)="cancelClick()"
      id="stripe-cancel-button"
    >
      Cancel
    </button>
  </div>
  <ng-template #noForm>
    <div class="messge">
      <p>Unable to load payment form.</p>
      <p>Try again in a minute. If it’s still not working give Member Help a shout.</p>
    </div>
  </ng-template>
</ng-template>

{"name": "cashless", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "test-coverage": "ng test cashless --reporters=junit --code-coverage --watch=false --browsers=ChromeHeadless", "bundle-report": "webpack-bundle-analyzer dist/cashless/stats.json", "electron": "electron .", "electron-dev": "node scripts/dev-electron.js", "electron-build": "node scripts/build-electron.js", "electron-build-win": "node scripts/build-electron.js win", "electron-build-mac": "node scripts/build-electron.js mac", "electron-build-linux": "node scripts/build-electron.js linux", "electron-build-all": "node scripts/build-electron.js all", "electron-pack": "npm run build -- --configuration=electron && electron-builder", "electron-pack-win": "npm run build -- --configuration=electron && electron-builder --win", "electron-pack-mac": "npm run build -- --configuration=electron && electron-builder --mac", "electron-pack-linux": "npm run build -- --configuration=electron && electron-builder --linux"}, "main": "electron-main.js", "homepage": "./", "private": true, "dependencies": {"@angular-devkit/build-angular": "^16.2.14", "@angular/animations": "^16.2.12", "@angular/cdk": "^16.2.12", "@angular/common": "^16.2.12", "@angular/compiler": "^16.2.12", "@angular/compiler-cli": "^16.2.12", "@angular/core": "^16.2.12", "@angular/fire": "^16.0.0", "@angular/forms": "^16.2.12", "@angular/material": "^16.2.12", "@angular/material-moment-adapter": "^16.2.12", "@angular/platform-browser": "^16.2.12", "@angular/platform-browser-dynamic": "^16.2.12", "@angular/router": "^16.2.12", "@angular/service-worker": "^16.2.12", "@braze/web-sdk": "^4.10.2", "@microsoft/applicationinsights-angularplugin-js": "^3.0.0", "@microsoft/applicationinsights-web": "^2.8.6", "@ngrx/effects": "^16.3.0", "@ngrx/entity": "^16.3.0", "@ngrx/store": "^16.3.0", "@ngrx/store-devtools": "^16.3.0", "@sentry/browser": "^5.7.1", "@stripe/stripe-js": "^3.5.0", "@supy-io/ngx-intercom": "^14.2.12", "@types/uuid": "^10.0.0", "braintree-web": "^3.55.0", "core-js": "^2.6.10", "export-to-csv": "^0.2.1", "file-saver": "^2.0.5", "firebase": "^10.7.0", "ink-html": "^1.0.4", "jspdf": "^2.5.1", "launchdarkly-js-client-sdk": "^3.1.3", "lodash": "^4.17.15", "moment": "^2.24.0", "ng-animate": "^0.3.4", "ngrx-store-localstorage": "^16.0.0", "ngx-cookie-service": "^2.1.0", "ngx-device-detector": "^6.0.2", "ngx-print": "^1.2.1", "ngx-stripe": "^16.4.0", "prettier": "^2.8.4", "rxjs": "^7.8.1", "tslib": "^2.0.0", "uuid": "^11.1.0", "webpack": "^5.88.1", "zone.js": "~0.13.3"}, "devDependencies": {"@angular-devkit/core": "^16.2.10", "@angular/cli": "^16.2.10", "@angular/language-service": "^16.2.12", "@types/file-saver": "^2.0.5", "@types/jasmine": "~4.0.0", "@types/jasminewd2": "^2.0.8", "@types/lodash": "^4.14.146", "@types/node": "^12.11.1", "codelyzer": "^6.0.0", "jasmine-core": "~4.2.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "karma-junit-reporter": "^2.0.1", "protractor": "~7.0.0", "ts-node": "~7.0.0", "tslint": "~6.1.0", "typescript": "^4.8.4", "webpack-bundle-analyzer": "^3.6.0"}, "build": {"appId": "com.spriggy.cashless-pos", "productName": "Spriggy Cashless POS", "directories": {"output": "dist-electron"}, "files": ["dist/cashless/**/*", "electron-main.js", "electron-preload.js", "node_modules/**/*"], "mac": {"category": "public.app-category.business", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}]}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}
@import '../../../../styles/cashless-theme.scss';

p {
  font-style: italic;

  &.description {
    margin-top: 0;
    margin-bottom: 30px;

    &.withMargin {
      margin-top: 20px;
    }

    & a {
      font-size: 14px;
    }
  }
}

.choicesContainer {
  margin-bottom: 20px;
  width: 100%;

  & td {
    text-align: center;
  }
}

.choiceAmount {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50px;
  width: 50px;
  border-radius: 50%;
  border: 1px solid $grey-2;
  text-align: center;
  color: black;
  background-color: $grey-4;
  cursor: pointer;

  &.active {
    background: linear-gradient(120.56deg, $orange-1 4.57%, $orange-2 97.51%);
    color: white;
    cursor: pointer;
  }
}

import { Injectable, OnD<PERSON>roy } from '@angular/core';
import { Observable, BehaviorSubject, Subject, interval } from 'rxjs';
import { takeUntil, filter } from 'rxjs/operators';
import { MultiScreenDetectionService } from './multi-screen-detection.service';
import { PosCommunicationService } from './pos-communication.service';
import { 
  ScreenInfo, 
  StudentWindowOpenRequestPayload, 
  StudentWindowOpenedPayload, 
  StudentWindowClosedPayload,
  WindowPositionUpdatePayload,
  PosMessageType
} from '../models/pos-messages.interface';

/**
 * Interface for student window configuration
 */
export interface StudentWindowConfig {
  width: number;
  height: number;
  targetScreen?: ScreenInfo;
  features?: string[];
}

/**
 * Interface for active student window information
 */
export interface ActiveStudentWindow {
  window: Window;
  windowId: string;
  screen: ScreenInfo;
  config: StudentWindowConfig;
  openedAt: number;
  isAlive: boolean;
}

/**
 * Service for managing student view windows on multiple screens
 */
@Injectable({
  providedIn: 'root'
})
export class StudentWindowManagerService implements OnDestroy {
  private activeWindowsSubject = new BehaviorSubject<ActiveStudentWindow[]>([]);
  private windowStatusSubject = new BehaviorSubject<'closed' | 'opening' | 'open' | 'error'>('closed');
  private destroy$ = new Subject<void>();
  private windowCheckInterval: any;
  private currentWindowGuid = this.generateWindowGuid();

  // Default window configuration
  private defaultConfig: StudentWindowConfig = {
    width: 1024,
    height: 768,
    features: [
      'width=1024',
      'height=768',
      'resizable=yes',
      'scrollbars=yes',
      'toolbar=no',
      'menubar=no',
      'location=no',
      'status=no'
    ]
  };

  constructor(
    private screenDetectionService: MultiScreenDetectionService,
    private communicationService: PosCommunicationService
  ) {
    this.setupMessageListeners();
    this.startWindowHealthCheck();
  }

  /**
   * Setup message listeners for cross-window communication
   */
  private setupMessageListeners(): void {
    // Listen for window open requests
    this.communicationService.onMessageOfType<StudentWindowOpenRequestPayload>(
      PosMessageType.STUDENT_WINDOW_OPEN_REQUEST
    ).pipe(
      takeUntil(this.destroy$)
    ).subscribe(message => {
      this.handleWindowOpenRequest(message.payload);
    });

    // Listen for window closed messages
    this.communicationService.onMessageOfType<StudentWindowClosedPayload>(
      PosMessageType.STUDENT_WINDOW_CLOSED
    ).pipe(
      takeUntil(this.destroy$)
    ).subscribe(message => {
      this.handleWindowClosedMessage(message.payload);
    });
  }

  /**
   * Start periodic health check for opened windows
   */
  private startWindowHealthCheck(): void {
    this.windowCheckInterval = setInterval(() => {
      this.checkWindowHealth();
    }, 2000);
  }

  /**
   * Check if opened windows are still alive
   */
  private checkWindowHealth(): void {
    const activeWindows = this.activeWindowsSubject.value;
    const aliveWindows = activeWindows.filter(windowInfo => {
      const isAlive = !windowInfo.window.closed;
      if (!isAlive && windowInfo.isAlive) {
        // Window was closed
        this.handleWindowClosed(windowInfo, 'user_action');
      }
      return isAlive;
    });

    if (aliveWindows.length !== activeWindows.length) {
      this.activeWindowsSubject.next(aliveWindows);
      this.updateWindowStatus();
    }
  }

  /**
   * Open student view window on specified screen
   */
  async openStudentWindow(config?: Partial<StudentWindowConfig>): Promise<boolean> {
    try {
      this.windowStatusSubject.next('opening');

      const finalConfig = { ...this.defaultConfig, ...config };
      
      // Determine target screen
      let targetScreen = finalConfig.targetScreen;
      if (!targetScreen) {
        const secondaryScreens = this.screenDetectionService.getSecondaryScreens();
        targetScreen = secondaryScreens.length > 0 ? secondaryScreens[0] : this.screenDetectionService.getPrimaryScreen();
      }

      if (!targetScreen) {
        throw new Error('No suitable screen found for student window');
      }

      // Calculate optimal position
      const position = this.screenDetectionService.calculateOptimalPosition(
        targetScreen,
        { width: finalConfig.width, height: finalConfig.height }
      );

      // Build window features string
      const features = [
        `width=${finalConfig.width}`,
        `height=${finalConfig.height}`,
        `left=${position.left}`,
        `top=${position.top}`,
        ...(finalConfig.features || [])
      ].join(',');

      // Open the window
      const studentWindow = window.open(
        `${window.location.origin}${window.location.pathname}#/canteen/order/place?view=student`,
        'studentView',
        features
      );

      if (!studentWindow) {
        throw new Error('Failed to open student window - popup blocked?');
      }

      // Create window info
      const windowInfo: ActiveStudentWindow = {
        window: studentWindow,
        windowId: this.generateWindowGuid(),
        screen: targetScreen,
        config: finalConfig,
        openedAt: Date.now(),
        isAlive: true
      };

      // Add to active windows
      const currentWindows = this.activeWindowsSubject.value;
      this.activeWindowsSubject.next([...currentWindows, windowInfo]);

      // Send window opened message
      this.communicationService.sendMessage(
        PosMessageType.STUDENT_WINDOW_OPENED,
        {
          windowId: windowInfo.windowId,
          screen: targetScreen,
          windowOptions: {
            width: finalConfig.width,
            height: finalConfig.height,
            left: position.left,
            top: position.top
          },
          openedAt: windowInfo.openedAt
        } as StudentWindowOpenedPayload,
        this.currentWindowGuid,
        'all'
      );

      this.windowStatusSubject.next('open');
      return true;

    } catch (error) {
      console.error('Failed to open student window:', error);
      this.windowStatusSubject.next('error');
      return false;
    }
  }

  /**
   * Close all student windows
   */
  closeAllStudentWindows(): void {
    const activeWindows = this.activeWindowsSubject.value;
    
    activeWindows.forEach(windowInfo => {
      if (!windowInfo.window.closed) {
        windowInfo.window.close();
        this.handleWindowClosed(windowInfo, 'parent_closed');
      }
    });

    this.activeWindowsSubject.next([]);
    this.windowStatusSubject.next('closed');
  }

  /**
   * Close specific student window
   */
  closeStudentWindow(windowId: string): void {
    const activeWindows = this.activeWindowsSubject.value;
    const windowInfo = activeWindows.find(w => w.windowId === windowId);
    
    if (windowInfo && !windowInfo.window.closed) {
      windowInfo.window.close();
      this.handleWindowClosed(windowInfo, 'parent_closed');
      
      const remainingWindows = activeWindows.filter(w => w.windowId !== windowId);
      this.activeWindowsSubject.next(remainingWindows);
      this.updateWindowStatus();
    }
  }

  /**
   * Handle window closed event
   */
  private handleWindowClosed(windowInfo: ActiveStudentWindow, reason: 'user_action' | 'parent_closed' | 'error'): void {
    windowInfo.isAlive = false;

    // Send window closed message
    this.communicationService.sendMessage(
      PosMessageType.STUDENT_WINDOW_CLOSED,
      {
        windowId: windowInfo.windowId,
        reason,
        closedAt: Date.now()
      } as StudentWindowClosedPayload,
      this.currentWindowGuid,
      'all'
    );
  }

  /**
   * Handle window open request from other windows
   */
  private handleWindowOpenRequest(payload: StudentWindowOpenRequestPayload): void {
    // Only the main window should handle open requests
    if (window.name !== '' && window.name !== 'studentView') {
      this.openStudentWindow({
        width: payload.windowOptions.width,
        height: payload.windowOptions.height,
        targetScreen: payload.targetScreen
      });
    }
  }

  /**
   * Handle window closed message from other windows
   */
  private handleWindowClosedMessage(payload: StudentWindowClosedPayload): void {
    const activeWindows = this.activeWindowsSubject.value;
    const remainingWindows = activeWindows.filter(w => w.windowId !== payload.windowId);
    
    if (remainingWindows.length !== activeWindows.length) {
      this.activeWindowsSubject.next(remainingWindows);
      this.updateWindowStatus();
    }
  }

  /**
   * Update window status based on active windows
   */
  private updateWindowStatus(): void {
    const activeWindows = this.activeWindowsSubject.value;
    const status = activeWindows.length > 0 ? 'open' : 'closed';
    this.windowStatusSubject.next(status);
  }

  /**
   * Generate unique window GUID
   */
  private generateWindowGuid(): string {
    return `window_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get active windows observable
   */
  getActiveWindows(): Observable<ActiveStudentWindow[]> {
    return this.activeWindowsSubject.asObservable();
  }

  /**
   * Get window status observable
   */
  getWindowStatus(): Observable<'closed' | 'opening' | 'open' | 'error'> {
    return this.windowStatusSubject.asObservable();
  }

  /**
   * Get active windows synchronously
   */
  getActiveWindowsSync(): ActiveStudentWindow[] {
    return this.activeWindowsSubject.value;
  }

  /**
   * Check if any student windows are open
   */
  hasOpenWindows(): boolean {
    return this.activeWindowsSubject.value.length > 0;
  }

  /**
   * Cleanup resources
   */
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    
    if (this.windowCheckInterval) {
      clearInterval(this.windowCheckInterval);
    }
    
    this.closeAllStudentWindows();
  }
}

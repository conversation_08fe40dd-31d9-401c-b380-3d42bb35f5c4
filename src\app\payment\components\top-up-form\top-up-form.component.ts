import {
  Component,
  OnInit,
  Output,
  EventEmitter,
  Input,
  SimpleChanges,
  OnChanges,
  SimpleChange,
  OnDestroy,
} from '@angular/core';
import { environment } from '../../../../environments/environment';

// Braintree
import * as braintree from 'braintree-web';

// models
import {
  BaseComponent,
  MakePaymentResponse,
  CreateTopUpRequest,
  AddCardForm,
  AddPaymentMethodAPIRequest,
  AddPaymentMethodsRequest,
  AddPaymentMethodResponse,
  PaypalSupplementaryData,
  UserCashless,
  TokenResponse,
  PaymentGatewayType,
} from 'src/app/sharedModels';

// services
import { PayService, UserService, CashlessAppInsightsService } from '../../../sharedServices';

@Component({
  selector: 'top-up-form',
  templateUrl: './top-up-form.component.html',
  styleUrls: ['./top-up-form.component.scss'],
})
export class TopUpFormComponent extends BaseComponent implements OnInit, OnChanges, On<PERSON><PERSON>roy {
  @Input() isNestedTopUp: boolean = false;
  @Input() topUpAmount: number;
  @Input() userBalance: number;
  @Output() PaymentSucceed: EventEmitter<boolean> = new EventEmitter();
  isLoading: boolean = true;
  private _device_data: string;
  private _selectedCard: string;
  showSubmitButton: boolean = false;
  ccNotAccepted: boolean = false;
  errorMessage: string;
  isAddCard: boolean = false;
  bClientInstance: any;
  waitingForBalanceUpdate: boolean = false;
  tokenData: TokenResponse;
  PaymentGatewayType = PaymentGatewayType;
  timeout: ReturnType<typeof setInterval>;
  DEFAULT_TOPUP_TIME_ARRAY = [2000, 2000, 2000, 2000];

  constructor(
    private payService: PayService,
    private userService: UserService,
    private appInsightsService: CashlessAppInsightsService
  ) {
    super();
  }

  ngOnInit(): void {
    this.getToken();
  }

  ngOnDestroy(): void {
    if (this.timeout) {
      clearInterval(this.timeout);
    }
  }

  ngOnChanges(simpleChanges: SimpleChanges): void {
    if (simpleChanges?.userBalance) {
      this.checkIfBalanceHasUpdatedAfterTopup(simpleChanges.userBalance);
    }
  }

  checkIfBalanceHasUpdatedAfterTopup(balanceUpdate: SimpleChange): void {
    if (!this.waitingForBalanceUpdate) {
      return;
    }
    if (balanceUpdate?.currentValue != balanceUpdate?.previousValue) {
      this.userBalanceUpdated();
    }
  }

  userBalanceUpdated(): void {
    clearInterval(this.timeout);
    this.PaymentSucceed.next(true);
  }

  //ONLY BT - can be removed once not using braintree anymore
  getToken(): void {
    this.isLoading = true;
    this.payService.GetNewToken().subscribe({
      next: (res: TokenResponse) => {
        if (res) {
          this.tokenData = res;
          this.createBraintreeUI();
        }
        this.ccNotAccepted = false;
        this.isLoading = false;
      },
      error: error => {
        this.isLoading = false;
        this.ccNotAccepted = true;
        this.handleErrorFromService(error);
      },
    });
  }

  cancelClick(): void {
    this.PaymentSucceed.next(false);
  }

  isBrainTreeUser(): boolean {
    return this.tokenData && this.tokenData?.gatewayType === PaymentGatewayType.SpriggyCore;
  }

  hideAddCard(): void {
    this.isAddCard = false;
    setTimeout(() => this.createBraintreeUI(), 500);
  }

  // BT ONLY
  addCardConfirmClick(request: AddCardForm): void {
    this.isLoading = true;
    this.ccNotAccepted = false;

    if (request.remember) {
      // save credit card
      let user = this.userService.GetUserConnected();
      const addCardRequest = this.getAddCardRequest(user, request.nonce);
      this.addPaymentMethodAndCompleteTopUp(addCardRequest);
      return;
    }
    this.topUp(request.nonce, null);
  }

  //BT ONLY
  addPaymentMethodAndCompleteTopUp(request: AddPaymentMethodAPIRequest): void {
    this.payService.AddPaymentMethodAPI(request).subscribe({
      next: (res: AddPaymentMethodResponse) => {
        if (res && res.isSuccess) {
          this.topUp(null, res.payment_method_id);
        } else {
          this.manageUIErrorResponse(res.message);
        }
      },
      error: error => {
        this.manageUIErrorResponse('');
        this.handleErrorFromService(error);
      },
    });
  }

  confirmClick(): void {
    this.topUp(null, this._selectedCard);
  }

  addCardClick(): void {
    this.isAddCard = !this.isAddCard;
  }

  cardSelected(methodId: string): void {
    this._selectedCard = methodId;
    this.showSubmitButton = Boolean(methodId);
  }

  topUp(nonce: string, paymentMethodId: string): void {
    this.isLoading = true;
    this.ccNotAccepted = false;
    const topUpRequest = this.getTopUpRequest(nonce, paymentMethodId);

    this.appInsightsService.TrackTrace('topUp_request_cashlessapi_CLIENT', {
      Request: JSON.stringify(topUpRequest),
    });

    this.payService.MakeTopupAPI(topUpRequest).subscribe({
      next: (response: MakePaymentResponse) => {
        if (response.isPaymentSuccess) {
          // After top up has completed there may be a delay in retrieving the updated user balance from Stripe
          // To ensure the UI gets the updated balance, the getBalance API will be called in 2 second intervals (stopped after 4 calls)
          // Any balance updates will be received in the onChange function
          this.processSuccessfulTopup(response);
          this.waitingForBalanceUpdate = true;
        } else {
          this.isLoading = false;
          this.manageUIErrorResponse(response.message);
        }
      },
      error: error => {
        this.isLoading = false;
        this.manageUIErrorResponse('');
        this.handleErrorFromService(error);
      },
    });
  }

  processSuccessfulTopup(response: MakePaymentResponse): void {
    const timeArrayFromApi = response.balanceRefreshIntervalsInMillis;
    const timeArray = timeArrayFromApi?.length > 0 ? timeArrayFromApi : this.DEFAULT_TOPUP_TIME_ARRAY;
    const maxRefreshCount = timeArray.length;
    this.refreshUserBalanceAfterDelay(0, maxRefreshCount, timeArray, this.userBalance);
  }

  refreshUserBalanceAfterDelay(
    index: number,
    maxRefreshCount: number,
    timeArray: number[],
    preTopUpBalance: number
  ): void {
    this.timeout = setTimeout(() => {
      this.payService.UpdateBalance();
      this.processUserBalanceRefresh(index, maxRefreshCount, timeArray, preTopUpBalance);
    }, timeArray[index]);
  }

  processUserBalanceRefresh(
    index: number,
    maxRefreshCount: number,
    time: number[],
    preTopUpBalance: number
  ): void {
    const refreshCount = index + 1;
    if (refreshCount < maxRefreshCount) {
      this.refreshUserBalanceAfterDelay(refreshCount, maxRefreshCount, time, preTopUpBalance);
      return;
    }
    // balance has not updated after all refresh tries //TODO: decide what to do here
    this.userBalanceUpdated();
  }

  //ONLY BT
  private manageUIErrorResponse(message: string): void {
    if (message.indexOf('Declined') >= 0 || message.indexOf('Payment type is not supported') >= 0) {
      this.errorMessage = message;
    } else {
      this.errorMessage = 'Something went wrong, please try again or contact us for more information.';
    }
    this.ccNotAccepted = true;
    this.isLoading = false;
    this.createBraintreeUI();
  }

  //ONLY BT
  createBraintreeUI(): void {
    if (!this.isBrainTreeUser()) {
      return;
    }
    var that = this;
    let braintreeEnv = 'sandbox';

    if (environment.production) {
      braintreeEnv = 'production';
    }

    braintree.client
      .create({
        authorization: this.tokenData.token,
      })
      .then(clientInstance => {
        this.bClientInstance = clientInstance;

        braintree.dataCollector
          .create({
            client: clientInstance,
            kount: true,
          })
          .then(function (dataCollectorInstance) {
            that._device_data = dataCollectorInstance.deviceData;
            dataCollectorInstance.teardown();
          });
      });
  }

  //////////////////////
  // GET REQUESTS
  //////////////////////

  getAddCardRequest(user: UserCashless, nonce: string): AddPaymentMethodAPIRequest {
    return {
      externalUserId: user.ExternalUserId,
      userId: user.UserId,
      paymentMethod: this.getPaymentMethodsRequest(nonce),
    };
  }

  getPaymentMethodsRequest(nonce: string): AddPaymentMethodsRequest {
    return {
      payment_method_nonce: nonce,
      device_data: this._device_data,
      make_default: true,
    };
  }

  private _GetSupplementaryData(): PaypalSupplementaryData {
    let user = this.userService.GetUserConnected();

    return {
      sender_account_id: user.UserId.toString(),
      sender_email: user.Email,
      sender_first_name: user.FirstName,
      sender_last_name: user.Lastname,
    };
  }

  getTopUpRequest(nonce: string, paymentMethodId: string): CreateTopUpRequest {
    let user = this.userService.GetUserConnected();

    return {
      Payment_Nonce: nonce,
      Payment_method_id: paymentMethodId,
      ChargeAmount: this.topUpAmount,
      ExternalUserId: this.userService.GetUserExternalId(),
      UserId: user.UserId,
      supplementary_data: this._GetSupplementaryData(),
      device_data: this._device_data,
    };
  }
}

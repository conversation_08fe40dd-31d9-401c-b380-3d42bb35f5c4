import { Component, OnInit, OnDestroy } from '@angular/core';
import { v4 as uuidv4 } from 'uuid';
import { ActivatedRoute } from '@angular/router';
import { FormGroup, FormControl } from '@angular/forms';

// ngrx
import { Store, select } from '@ngrx/store';
import { Subscription, combineLatest } from 'rxjs';
import { CanteenState } from '../../../states';
import { canteenStateSelector } from '../../../states/canteen/canteen.selectors';
import * as canteenActions from '../../../states/canteen/canteen.actions';

// Models
import { UserCashless, Canteen, CanteenSchool } from '../../../sharedModels';

// Multi-screen services
import { MultiScreenDetectionService } from '../../services/multi-screen-detection.service';
import { StudentWindowManagerService } from '../../services/student-window-manager.service';
import { ElectronService, ElectronDisplay } from '../../services/electron.service';
import { ScreenInfo } from '../../models/pos-messages.interface';

@Component({
  selector: 'app-pos',
  templateUrl: './pos.component.html',
  styleUrls: ['./pos.component.scss'],
})
export class PosComponent implements OnInit, OnDestroy {
  SCHOOL_ID = 52243;
  showStudentDropdown = false;
  selectedStudent: UserCashless | null = null;

  // Merchant dropdown properties
  merchantFormGroup: FormGroup;
  listCanteens: Canteen[] = [];
  canteenListVisible: boolean = true;
  currentCanteenId: number;

  // School dropdown properties
  schoolFormGroup: FormGroup;
  listSchools: CanteenSchool[] = [];

  // Multi-screen properties
  availableScreens: ScreenInfo[] = [];
  isMultiScreenSupported = false;
  studentWindowStatus: 'closed' | 'opening' | 'open' | 'error' = 'closed';
  selectedScreen: ScreenInfo | null = null;

  // Electron properties
  isElectronApp = false;
  electronDisplays: ElectronDisplay[] = [];
  selectedElectronDisplay: ElectronDisplay | null = null;

  // Subscriptions
  private subscription: Subscription;
  private multiScreenSubscription: Subscription;
  private listLoaded: boolean = false;
  private selectedCanteen: number;

  constructor(
    private route: ActivatedRoute,
    private canteenStore: Store<{ canteen: CanteenState }>,
    private screenDetectionService: MultiScreenDetectionService,
    private studentWindowManager: StudentWindowManagerService,
    private electronService: ElectronService
  ) {}

  ngOnInit(): void {
    // Check if we're coming from the canteen/students route
    this.route.url.subscribe(() => {
      this.showStudentDropdown = window.location.pathname.includes('canteen/students');
    });

    // Initialize multi-screen functionality
    this.initializeMultiScreen();

    // Initialize merchant dropdown
    this.subscription = this.canteenStore
      .pipe(select(canteenStateSelector))
      .subscribe((state: CanteenState) => {
        this.currentCanteenId = state?.selected?.CanteenId;
        if (!this.listLoaded) {
          this.listCanteens = [...state.list];

          if (state.dataLoaded) {
            this.listLoaded = true;

            if (this.listCanteens && this.listCanteens.length > 0) {
              this.canteenListVisible = this.listCanteens != null && this.listCanteens.length > 1;
              this.createMerchantForm(state.selected);

              // Initialize school dropdown if merchant is selected
              if (state.selected) {
                this.initializeSchoolDropdown(state);
              }
              return;
            }
          }
        }

        // Update school dropdown when merchant changes
        if (state.selected && state.selected.CanteenId !== this.selectedCanteen) {
          this.selectedCanteen = state.selected.CanteenId;
          this.initializeSchoolDropdown(state);
        }
      });
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
    if (this.multiScreenSubscription) {
      this.multiScreenSubscription.unsubscribe();
    }
  }

  /**
   * Initialize multi-screen functionality
   */
  private initializeMultiScreen(): void {
    // Check if running in Electron
    this.isElectronApp = this.electronService.isElectron();

    if (this.isElectronApp) {
      // Use Electron's display API
      this.initializeElectronMultiScreen();
    } else {
      // Use browser-based multi-screen detection
      this.initializeBrowserMultiScreen();
    }
  }

  /**
   * Initialize Electron-based multi-screen functionality
   */
  private initializeElectronMultiScreen(): void {
    this.multiScreenSubscription = combineLatest([
      this.electronService.getDisplays(),
      this.studentWindowManager.getWindowStatus()
    ]).subscribe(([displays, windowStatus]) => {
      this.electronDisplays = displays;
      this.isMultiScreenSupported = displays.length > 1;
      this.studentWindowStatus = windowStatus;

      // Set default selected display to first secondary display
      if (this.isMultiScreenSupported) {
        const secondaryDisplays = displays.filter(d => !d.isPrimary);
        this.selectedElectronDisplay = secondaryDisplays.length > 0 ? secondaryDisplays[0] : null;
      }
    });
  }

  /**
   * Initialize browser-based multi-screen functionality
   */
  private initializeBrowserMultiScreen(): void {
    this.multiScreenSubscription = combineLatest([
      this.screenDetectionService.getScreens(),
      this.screenDetectionService.isMultiScreenSupported(),
      this.studentWindowManager.getWindowStatus()
    ]).subscribe(([screens, isSupported, windowStatus]) => {
      this.availableScreens = screens;
      this.isMultiScreenSupported = isSupported;
      this.studentWindowStatus = windowStatus;

      // Set default selected screen to first secondary screen
      if (isSupported && screens.length > 1) {
        const secondaryScreens = screens.filter(s => !s.isPrimary);
        this.selectedScreen = secondaryScreens.length > 0 ? secondaryScreens[0] : null;
      }
    });
  }

  openMerchantView(): void {
    this.openPosTab('merchant');
  }

  openStudentView(): void {
    this.openPosTab('student');
  }

  /**
   * Open student view on secondary screen
   */
  openStudentViewOnSecondaryScreen(): void {
    if (this.isElectronApp && this.selectedElectronDisplay) {
      this.openElectronStudentWindow();
    } else if (this.selectedScreen) {
      this.studentWindowManager.openStudentWindow({
        width: 1024,
        height: 768,
        targetScreen: this.selectedScreen
      });
    }
  }

  /**
   * Open student window using Electron
   */
  private async openElectronStudentWindow(): Promise<void> {
    if (!this.selectedElectronDisplay) return;

    try {
      const position = this.electronService.calculateOptimalPosition(
        this.selectedElectronDisplay,
        { width: 1024, height: 768 }
      );

      const result = await this.electronService.openStudentWindow(
        this.selectedElectronDisplay.id,
        {
          width: 1024,
          height: 768,
          x: position.x,
          y: position.y
        }
      );

      if (result.success) {
        console.log('Electron student window opened:', result.windowId);
      } else {
        console.error('Failed to open Electron student window:', result.error);
      }
    } catch (error) {
      console.error('Error opening Electron student window:', error);
    }
  }

  /**
   * Close all student windows
   */
  closeStudentWindows(): void {
    if (this.isElectronApp) {
      this.electronService.closeAllStudentWindows();
    } else {
      this.studentWindowManager.closeAllStudentWindows();
    }
  }

  /**
   * Handle screen selection change
   */
  onScreenSelectionChange(screen: ScreenInfo): void {
    this.selectedScreen = screen;
  }

  /**
   * Handle Electron display selection change
   */
  onElectronDisplaySelectionChange(display: ElectronDisplay): void {
    this.selectedElectronDisplay = display;
  }

  /**
   * Check if student windows are currently open
   */
  get hasOpenStudentWindows(): boolean {
    if (this.isElectronApp) {
      // For Electron, we'll need to check async, but for now return based on status
      return this.studentWindowStatus === 'open';
    }
    return this.studentWindowManager.hasOpenWindows();
  }

  /**
   * Get secondary screens for display
   */
  get secondaryScreens(): ScreenInfo[] {
    return this.availableScreens.filter(screen => !screen.isPrimary);
  }

  /**
   * Get secondary Electron displays for display
   */
  get secondaryElectronDisplays(): ElectronDisplay[] {
    return this.electronDisplays.filter(display => !display.isPrimary);
  }

  private openPosTab(viewType: 'merchant' | 'student'): void {
    const guid = uuidv4();
    const url = `/canteen/pos/tab?schoolId=${this.SCHOOL_ID}&guid=${guid}&viewType=${viewType}`;

    // Open in new tab
    window.open(url, '_blank');
  }

  onStudentSelected(student: UserCashless): void {
    this.selectedStudent = student;
    if (student) {
      // Open student view with the selected student
      const guid = uuidv4();
      const url = `/canteen/pos/tab?schoolId=${this.SCHOOL_ID}&guid=${guid}&viewType=student&userId=${student.UserId}`;
      window.open(url, '_blank');
    }
  }

  private createMerchantForm(selectedCanteen: Canteen): void {
    let checkExist = -1;

    if (selectedCanteen) {
      checkExist = this.listCanteens.findIndex(x => x.CanteenId == selectedCanteen.CanteenId);
    }

    // check if selected exist in the list
    let selected = this.listCanteens[0].CanteenId;

    if (checkExist >= 0) {
      selected = selectedCanteen.CanteenId;
    }

    this.merchantFormGroup = new FormGroup({
      canteen: new FormControl(selected),
    });

    // set selected
    this.setSelectedCanteen(selected);

    this.canteen.valueChanges.subscribe(val => {
      this.setSelectedCanteen(val);
    });
  }

  private initializeSchoolDropdown(state: CanteenState): void {
    if (state.selected && state.selected.Schools && state.selected.Schools.length > 0) {
      this.listSchools = state.selected.Schools.slice().sort((a, b) => {
        if (a.Name < b.Name) return -1;
        if (a.Name > b.Name) return 1;
        else return 0;
      });
      this.createSchoolForm(state.selectedSchool);
    }
  }

  private createSchoolForm(selectedSchool: CanteenSchool): void {
    let checkExist = -1;

    if (selectedSchool) {
      checkExist = this.listSchools.findIndex(x => x.SchoolId == selectedSchool.SchoolId);
    }

    // check if selected exist in the list
    let selected = this.listSchools[0].SchoolId;

    if (checkExist >= 0) {
      selected = selectedSchool.SchoolId;
    }

    this.schoolFormGroup = new FormGroup({
      school: new FormControl(selected),
    });

    // set selected
    this.setSelectedSchool(selected);

    this.school.valueChanges.subscribe(val => {
      this.setSelectedSchool(val);
    });
  }

  get canteen() {
    return this.merchantFormGroup.get('canteen');
  }

  get school() {
    return this.schoolFormGroup.get('school');
  }

  private setSelectedCanteen(id: number): void {
    let canteen = this.listCanteens.find(x => x.CanteenId == id);
    // dispatch new canteen if new canteen does not match the canteen in state
    if (canteen && canteen.CanteenId != this.currentCanteenId) {
      this.canteenStore.dispatch(canteenActions.SetSelectedCanteen({ canteen: canteen }));
    }
  }

  private setSelectedSchool(id: number): void {
    let school = this.listSchools.find(x => x.SchoolId == id);
    if (school) {
      // Update the SCHOOL_ID when a school is selected
      this.SCHOOL_ID = school.SchoolId;
      this.canteenStore.dispatch(canteenActions.SetSelectedSchool({ school: school }));
    }
  }
}

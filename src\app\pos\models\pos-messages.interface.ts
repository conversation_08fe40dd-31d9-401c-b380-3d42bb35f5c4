import { UserCashless } from 'src/app/sharedModels';

/**
 * Interface for cross-tab communication messages in POS system
 */
export interface PosMessage {
  type: PosMessageType;
  payload: any;
  timestamp: number;
  sourceGuid: string;
  targetViewType?: 'merchant' | 'student' | 'all';
}

/**
 * Enum defining all possible message types for POS cross-tab communication
 */
export enum PosMessageType {
  ORDER_PLACED = 'ORDER_PLACED',
  STUDENT_SELECTED = 'STUDENT_SELECTED',
  MENU_CHANGED = 'MENU_CHANGED',
  CART_UPDATED = 'CART_UPDATED',
  CART_CLEARED = 'CART_CLEARED',
  VIEW_REFRESH = 'VIEW_REFRESH',
  CONNECTION_STATUS = 'CONNECTION_STATUS',
  ITEM_POPUP_OPENED = 'ITEM_POPUP_OPENED',
  ITEM_POPUP_CLOSED = 'ITEM_POPUP_CLOSED',
  CATEGORY_CHANGED = 'CATEGORY_CHANGED',
  BALANCE_UPDATED = 'BALANCE_UPDATED',

  // Multi-screen support message types
  SCREEN_DETECTION_REQUEST = 'SCREEN_DETECTION_REQUEST',
  SCREEN_DETECTION_RESPONSE = 'SCREEN_DETECTION_RESPONSE',
  STUDENT_WINDOW_OPEN_REQUEST = 'STUDENT_WINDOW_OPEN_REQUEST',
  STUDENT_WINDOW_OPENED = 'STUDENT_WINDOW_OPENED',
  STUDENT_WINDOW_CLOSED = 'STUDENT_WINDOW_CLOSED',
  WINDOW_POSITION_UPDATE = 'WINDOW_POSITION_UPDATE',
  DISPLAY_PREFERENCES_CHANGED = 'DISPLAY_PREFERENCES_CHANGED'
}

/**
 * Payload for order placement messages
 */
export interface OrderPlacedPayload {
  orderId: number;
  studentId: number;
  studentName: string;
  menuType: string;
  orderDate: string;
  totalAmount: number;
  itemCount: number;
  paymentMethod?: string;
}

/**
 * Payload for student selection messages
 */
export interface StudentSelectedPayload {
  student: UserCashless | null;
}

/**
 * Payload for menu change messages
 */
export interface MenuChangedPayload {
  menuType: string;
  orderDate: Date;
  studentId?: number;
  menuName?: string;
}

/**
 * Payload for cart update messages
 */
export interface CartUpdatedPayload {
  itemCount: number;
  totalAmount: number;
  studentId?: number;
  cartItems: any[]; // Full cart items array for real-time sync
  timestamp: string;
}

/**
 * Payload for view refresh messages
 */
export interface ViewRefreshPayload {
  reason: string;
  data?: any;
}

/**
 * Payload for connection status messages
 */
export interface ConnectionStatusPayload {
  isConnected: boolean;
  tabCount: number;
}

/**
 * Payload for item popup opened messages
 */
export interface ItemPopupOpenedPayload {
  item: any; // MenuItem
  date: Date;
  category: any; // Category
}

/**
 * Payload for item popup closed messages
 */
export interface ItemPopupClosedPayload {
  itemAdded: boolean;
  item?: any; // OrderItem if added to cart
}

/**
 * Payload for category change messages
 */
export interface CategoryChangedPayload {
  category: any; // Category
  categoryId: number;
  categoryName: string;
}

/**
 * Payload for balance update messages
 */
export interface BalanceUpdatedPayload {
  studentId: number;
  balance: number;
  studentName: string;
  favoriteColor?: string;
  isGuest?: boolean;
}

/**
 * Payload for cart cleared messages
 */
export interface CartClearedPayload {
  reason: 'student_changed' | 'manual_clear' | 'no_student_selected';
  previousStudentId?: number;
  newStudentId?: number;
}

/**
 * Interface for screen/display information
 */
export interface ScreenInfo {
  id: string;
  name: string;
  width: number;
  height: number;
  availWidth: number;
  availHeight: number;
  left: number;
  top: number;
  isPrimary: boolean;
  colorDepth: number;
  pixelDepth: number;
}

/**
 * Payload for screen detection request messages
 */
export interface ScreenDetectionRequestPayload {
  requestId: string;
  requestingWindowId: string;
}

/**
 * Payload for screen detection response messages
 */
export interface ScreenDetectionResponsePayload {
  requestId: string;
  screens: ScreenInfo[];
  currentScreen: ScreenInfo;
  respondingWindowId: string;
}

/**
 * Payload for student window open request messages
 */
export interface StudentWindowOpenRequestPayload {
  targetScreen: ScreenInfo;
  windowOptions: {
    width: number;
    height: number;
    left: number;
    top: number;
  };
  requestingWindowId: string;
}

/**
 * Payload for student window opened messages
 */
export interface StudentWindowOpenedPayload {
  windowId: string;
  screen: ScreenInfo;
  windowOptions: {
    width: number;
    height: number;
    left: number;
    top: number;
  };
  openedAt: number;
}

/**
 * Payload for student window closed messages
 */
export interface StudentWindowClosedPayload {
  windowId: string;
  reason: 'user_action' | 'parent_closed' | 'error';
  closedAt: number;
}

/**
 * Payload for window position update messages
 */
export interface WindowPositionUpdatePayload {
  windowId: string;
  position: {
    left: number;
    top: number;
    width: number;
    height: number;
  };
  screen: ScreenInfo;
}

/**
 * Payload for display preferences changed messages
 */
export interface DisplayPreferencesChangedPayload {
  preferences: {
    preferredStudentScreen?: string;
    autoOpenStudentView: boolean;
    studentViewSize: {
      width: number;
      height: number;
    };
    rememberWindowPositions: boolean;
  };
  changedBy: string;
}

/**
 * Type guard to check if a message is of a specific type
 */
export function isMessageOfType<T>(
  message: PosMessage,
  type: PosMessageType
): message is PosMessage & { payload: T } {
  return message.type === type;
}

/**
 * Helper function to create a POS message
 */
export function createPosMessage(
  type: PosMessageType,
  payload: any,
  sourceGuid: string,
  targetViewType?: 'merchant' | 'student' | 'all'
): PosMessage {
  return {
    type,
    payload,
    timestamp: Date.now(),
    sourceGuid,
    targetViewType
  };
}

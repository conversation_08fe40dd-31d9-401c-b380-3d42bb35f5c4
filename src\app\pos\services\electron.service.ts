import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject, fromEvent } from 'rxjs';

/**
 * Interface for Electron display information
 */
export interface ElectronDisplay {
  id: number;
  label: string;
  bounds: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  workArea: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  scaleFactor: number;
  rotation: number;
  isPrimary: boolean;
}

/**
 * Interface for student window options
 */
export interface StudentWindowOptions {
  width?: number;
  height?: number;
  x?: number;
  y?: number;
}

/**
 * Interface for active student window info
 */
export interface ActiveStudentWindow {
  windowId: string;
  bounds: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  isVisible: boolean;
  isFocused: boolean;
}

/**
 * Service for interacting with Electron APIs
 */
@Injectable({
  providedIn: 'root'
})
export class ElectronService {
  private displaysSubject = new BehaviorSubject<ElectronDisplay[]>([]);
  private isElectronApp = false;

  constructor() {
    this.isElectronApp = this.checkElectronEnvironment();
    if (this.isElectronApp) {
      this.initializeElectronListeners();
      this.loadInitialDisplays();
    }
  }

  /**
   * Check if running in Electron environment
   */
  private checkElectronEnvironment(): boolean {
    return !!(window as any).electronAPI;
  }

  /**
   * Initialize Electron event listeners
   */
  private initializeElectronListeners(): void {
    if (!this.isElectronApp) return;

    // Listen for display changes
    (window as any).electronAPI.onDisplaysChanged((displays: ElectronDisplay[]) => {
      this.displaysSubject.next(displays);
    });

    // Listen for student window closed events
    (window as any).electronAPI.onStudentWindowClosed((data: any) => {
      console.log('Student window closed:', data);
      // You can emit events here if needed
    });
  }

  /**
   * Load initial display information
   */
  private async loadInitialDisplays(): Promise<void> {
    if (!this.isElectronApp) return;

    try {
      const displays = await (window as any).electronAPI.getDisplays();
      this.displaysSubject.next(displays);
    } catch (error) {
      console.error('Failed to load initial displays:', error);
    }
  }

  /**
   * Check if running in Electron
   */
  isElectron(): boolean {
    return this.isElectronApp;
  }

  /**
   * Get available displays
   */
  getDisplays(): Observable<ElectronDisplay[]> {
    return this.displaysSubject.asObservable();
  }

  /**
   * Get displays synchronously
   */
  getDisplaysSync(): ElectronDisplay[] {
    return this.displaysSubject.value;
  }

  /**
   * Refresh display information
   */
  async refreshDisplays(): Promise<ElectronDisplay[]> {
    if (!this.isElectronApp) return [];

    try {
      const displays = await (window as any).electronAPI.getDisplays();
      this.displaysSubject.next(displays);
      return displays;
    } catch (error) {
      console.error('Failed to refresh displays:', error);
      return [];
    }
  }

  /**
   * Open student window on specific display
   */
  async openStudentWindow(displayId: number, options?: StudentWindowOptions): Promise<{ success: boolean; windowId?: string; error?: string }> {
    if (!this.isElectronApp) {
      return { success: false, error: 'Not running in Electron' };
    }

    try {
      const result = await (window as any).electronAPI.openStudentWindow(displayId, options);
      return result;
    } catch (error) {
      console.error('Failed to open student window:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Close specific student window
   */
  async closeStudentWindow(windowId: string): Promise<{ success: boolean; error?: string }> {
    if (!this.isElectronApp) {
      return { success: false, error: 'Not running in Electron' };
    }

    try {
      const result = await (window as any).electronAPI.closeStudentWindow(windowId);
      return result;
    } catch (error) {
      console.error('Failed to close student window:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Close all student windows
   */
  async closeAllStudentWindows(): Promise<{ success: boolean; closedCount?: number; error?: string }> {
    if (!this.isElectronApp) {
      return { success: false, error: 'Not running in Electron' };
    }

    try {
      const result = await (window as any).electronAPI.closeAllStudentWindows();
      return result;
    } catch (error) {
      console.error('Failed to close all student windows:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get active student windows
   */
  async getActiveStudentWindows(): Promise<ActiveStudentWindow[]> {
    if (!this.isElectronApp) return [];

    try {
      const windows = await (window as any).electronAPI.getActiveStudentWindows();
      return windows;
    } catch (error) {
      console.error('Failed to get active student windows:', error);
      return [];
    }
  }

  /**
   * Get secondary displays (non-primary)
   */
  getSecondaryDisplays(): ElectronDisplay[] {
    return this.displaysSubject.value.filter(display => !display.isPrimary);
  }

  /**
   * Get primary display
   */
  getPrimaryDisplay(): ElectronDisplay | null {
    return this.displaysSubject.value.find(display => display.isPrimary) || null;
  }

  /**
   * Check if multi-display setup is available
   */
  hasMultipleDisplays(): boolean {
    return this.displaysSubject.value.length > 1;
  }

  /**
   * Get platform information
   */
  getPlatform(): string {
    if (!this.isElectronApp) return 'web';
    return (window as any).nodeAPI?.platform || 'unknown';
  }

  /**
   * Get Electron version
   */
  getElectronVersion(): string {
    if (!this.isElectronApp) return '';
    return (window as any).electronAPI?.getVersion() || '';
  }

  /**
   * Calculate optimal window position for display
   */
  calculateOptimalPosition(display: ElectronDisplay, windowSize: { width: number; height: number }): { x: number; y: number } {
    const centerX = display.workArea.x + (display.workArea.width - windowSize.width) / 2;
    const centerY = display.workArea.y + (display.workArea.height - windowSize.height) / 2;

    return {
      x: Math.max(display.workArea.x, Math.min(centerX, display.workArea.x + display.workArea.width - windowSize.width)),
      y: Math.max(display.workArea.y, Math.min(centerY, display.workArea.y + display.workArea.height - windowSize.height))
    };
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.isElectronApp) {
      (window as any).electronAPI?.removeAllListeners('displays-changed');
      (window as any).electronAPI?.removeAllListeners('student-window-closed');
    }
  }
}

const { app, BrowserWindow, screen, ipcMain, Menu } = require('electron');
const path = require('path');
const http = require('http');

// Check if we're in development mode
const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged;

// Keep a global reference of the window objects
let mainWindow;
let studentWindows = new Map();

/**
 * Wait for Angular dev server to be ready
 */
async function waitForAngularServer(url, maxAttempts = 30) {
  console.log(`🔍 Checking if Angular server is ready at ${url}...`);

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      await new Promise((resolve, reject) => {
        const req = http.get(url, (res) => {
          if (res.statusCode === 200) {
            resolve(res);
          } else {
            reject(new Error(`HTTP ${res.statusCode}`));
          }
        });

        req.on('error', reject);
        req.setTimeout(2000, () => {
          req.destroy();
          reject(new Error('Timeout'));
        });
      });

      console.log('✅ Angular server is ready!');
      return true;
    } catch (error) {
      console.log(`⏳ Attempt ${attempt}/${maxAttempts} - Angular server not ready yet...`);
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  throw new Error('Angular server failed to start within timeout');
}

/**
 * Create the main application window
 */
async function createMainWindow() {
  // Get primary display dimensions
  const primaryDisplay = screen.getPrimaryDisplay();
  const { width, height } = primaryDisplay.workAreaSize;

  // Create the browser window
  mainWindow = new BrowserWindow({
    width: Math.min(1200, width - 100),
    height: Math.min(800, height - 100),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'electron-preload.js'),
      webSecurity: true,
      allowRunningInsecureContent: false,
      experimentalFeatures: false
    },
    show: false,
    titleBarStyle: 'default',
    title: 'Spriggy Cashless POS'
  });

  // Determine the URL to load
  let startUrl;
  if (isDev) {
    const baseUrl = 'http://localhost:4200';
    try {
      // Wait for Angular server to be ready
      console.log('⏳ Waiting for Angular server to be ready...');
      await waitForAngularServer(baseUrl);

      // Additional wait to ensure Angular is fully loaded
      console.log('⏳ Waiting additional time for Angular to fully initialize...');
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Load the root URL first, then navigate to POS
      startUrl = baseUrl;
    } catch (error) {
      console.error('❌ Failed to connect to Angular dev server:', error.message);
      // Fallback to test page
      startUrl = `file://${path.join(__dirname, 'electron-test.html')}`;
    }
  } else {
    startUrl = `file://${path.join(__dirname, '../dist/cashless/index.html')}`;
  }

  console.log(`🌐 Loading URL: ${startUrl}`);

  // Load the app with error handling
  try {
    console.log('🔄 Attempting to load URL...');
    await mainWindow.loadURL(startUrl);
    console.log('✅ URL loaded successfully');
  } catch (error) {
    console.error('❌ Failed to load URL:', error);
    console.error('Error details:', {
      message: error.message,
      code: error.code,
      stack: error.stack
    });

    // Load error page
    const errorHtml = `
      <html>
        <head><title>Loading Error</title></head>
        <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
          <h1>Failed to Load Application</h1>
          <p>Error: ${error.message}</p>
          <p>Code: ${error.code || 'Unknown'}</p>
          <button onclick="location.reload()" style="padding: 10px 20px; font-size: 16px;">Retry</button>
          <br><br>
          <button onclick="require('electron').shell.openExternal('http://localhost:4200')" style="padding: 10px 20px; font-size: 16px;">Open in Browser</button>
        </body>
      </html>
    `;
    await mainWindow.loadURL(`data:text/html,${encodeURIComponent(errorHtml)}`);
  }

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();

    // Open DevTools in development
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  // Handle navigation errors
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    console.error(`❌ Failed to load ${validatedURL}: ${errorDescription} (${errorCode})`);

    if (isDev && validatedURL.includes('localhost:4200')) {
      const retryHtml = `
        <html>
          <head><title>Connection Error</title></head>
          <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
            <h1>Cannot Connect to Angular Dev Server</h1>
            <p>Make sure the Angular development server is running:</p>
            <code style="background: #f0f0f0; padding: 10px; display: block; margin: 20px;">npm start</code>
            <button onclick="location.reload()" style="padding: 10px 20px; font-size: 16px; margin: 10px;">Retry</button>
            <button onclick="require('electron').shell.openExternal('http://localhost:4200')" style="padding: 10px 20px; font-size: 16px; margin: 10px;">Open in Browser</button>
          </body>
        </html>
      `;
      mainWindow.loadURL(`data:text/html,${encodeURIComponent(retryHtml)}`);
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
    // Close all student windows when main window closes
    studentWindows.forEach(window => {
      if (!window.isDestroyed()) {
        window.close();
      }
    });
    studentWindows.clear();
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    require('electron').shell.openExternal(url);
    return { action: 'deny' };
  });

  // Add reload functionality
  mainWindow.webContents.on('before-input-event', (event, input) => {
    if (input.control && input.key.toLowerCase() === 'r') {
      mainWindow.reload();
    }
  });
}

/**
 * Get information about all available displays
 */
function getDisplayInfo() {
  const displays = screen.getAllDisplays();
  const primaryDisplay = screen.getPrimaryDisplay();
  
  return displays.map(display => ({
    id: display.id,
    label: display.label || `Display ${display.id}`,
    bounds: display.bounds,
    workArea: display.workArea,
    scaleFactor: display.scaleFactor,
    rotation: display.rotation,
    isPrimary: display.id === primaryDisplay.id
  }));
}

/**
 * Setup IPC handlers for communication with renderer processes
 */
function setupIpcHandlers() {
  // Get display information
  ipcMain.handle('get-displays', () => {
    return getDisplayInfo();
  });

  // Placeholder for other handlers
  ipcMain.handle('open-student-window', (_, { displayId, options }) => {
    console.log('Open student window requested:', displayId, options);
    return { success: true, windowId: 'test-window' };
  });

  ipcMain.handle('close-student-window', (_, { windowId }) => {
    console.log('Close student window requested:', windowId);
    return { success: true };
  });

  ipcMain.handle('close-all-student-windows', () => {
    console.log('Close all student windows requested');
    return { success: true, closedCount: 0 };
  });

  ipcMain.handle('get-active-student-windows', () => {
    return [];
  });

  // Hardware device placeholders
  ipcMain.handle('get-serial-ports', async () => {
    console.log('Get serial ports requested - returning empty list');
    return [];
  });

  ipcMain.handle('open-serial-port', async (_, { path, options }) => {
    console.log('Open serial port requested:', path, options);
    return { success: false, error: 'Hardware features not available in simple mode' };
  });

  ipcMain.handle('close-serial-port', async (_, { portId }) => {
    console.log('Close serial port requested:', portId);
    return { success: false, error: 'Hardware features not available in simple mode' };
  });

  ipcMain.handle('write-to-serial-port', async (_, { portId, data }) => {
    console.log('Write to serial port requested:', portId, data);
    return { success: false, error: 'Hardware features not available in simple mode' };
  });
}

/**
 * Create application menu
 */
function createMenu() {
  const template = [
    {
      label: 'File',
      submenu: [
        {
          label: 'Exit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// App event handlers
app.whenReady().then(async () => {
  await createMainWindow();
  setupIpcHandlers();
  createMenu();

  // Setup display change listeners after app is ready
  screen.on('display-added', () => {
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('displays-changed', getDisplayInfo());
    }
  });

  screen.on('display-removed', () => {
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('displays-changed', getDisplayInfo());
    }
  });

  screen.on('display-metrics-changed', () => {
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('displays-changed', getDisplayInfo());
    }
  });

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createMainWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

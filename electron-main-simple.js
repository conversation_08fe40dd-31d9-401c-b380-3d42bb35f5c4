const { app, BrowserWindow, screen, ipcMain, Menu } = require('electron');
const path = require('path');

// Check if we're in development mode
const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged;

// Keep a global reference of the window objects
let mainWindow;
let studentWindows = new Map();

/**
 * Create the main application window
 */
function createMainWindow() {
  // Get primary display dimensions
  const primaryDisplay = screen.getPrimaryDisplay();
  const { width, height } = primaryDisplay.workAreaSize;

  // Create the browser window
  mainWindow = new BrowserWindow({
    width: Math.min(1200, width - 100),
    height: Math.min(800, height - 100),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'electron-preload.js')
    },
    show: false,
    titleBarStyle: 'default'
  });

  // Load the app
  const startUrl = isDev 
    ? 'http://localhost:4200' 
    : `file://${path.join(__dirname, '../dist/cashless/index.html')}`;
  
  mainWindow.loadURL(startUrl);

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // Open DevTools in development
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
    // Close all student windows when main window closes
    studentWindows.forEach(window => {
      if (!window.isDestroyed()) {
        window.close();
      }
    });
    studentWindows.clear();
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    require('electron').shell.openExternal(url);
    return { action: 'deny' };
  });
}

/**
 * Get information about all available displays
 */
function getDisplayInfo() {
  const displays = screen.getAllDisplays();
  const primaryDisplay = screen.getPrimaryDisplay();
  
  return displays.map(display => ({
    id: display.id,
    label: display.label || `Display ${display.id}`,
    bounds: display.bounds,
    workArea: display.workArea,
    scaleFactor: display.scaleFactor,
    rotation: display.rotation,
    isPrimary: display.id === primaryDisplay.id
  }));
}

/**
 * Setup IPC handlers for communication with renderer processes
 */
function setupIpcHandlers() {
  // Get display information
  ipcMain.handle('get-displays', () => {
    return getDisplayInfo();
  });

  // Placeholder for other handlers
  ipcMain.handle('open-student-window', (_, { displayId, options }) => {
    console.log('Open student window requested:', displayId, options);
    return { success: true, windowId: 'test-window' };
  });

  ipcMain.handle('close-student-window', (_, { windowId }) => {
    console.log('Close student window requested:', windowId);
    return { success: true };
  });

  ipcMain.handle('close-all-student-windows', () => {
    console.log('Close all student windows requested');
    return { success: true, closedCount: 0 };
  });

  ipcMain.handle('get-active-student-windows', () => {
    return [];
  });

  // Hardware device placeholders
  ipcMain.handle('get-serial-ports', async () => {
    console.log('Get serial ports requested - returning empty list');
    return [];
  });

  ipcMain.handle('open-serial-port', async (_, { path, options }) => {
    console.log('Open serial port requested:', path, options);
    return { success: false, error: 'Hardware features not available in simple mode' };
  });

  ipcMain.handle('close-serial-port', async (_, { portId }) => {
    console.log('Close serial port requested:', portId);
    return { success: false, error: 'Hardware features not available in simple mode' };
  });

  ipcMain.handle('write-to-serial-port', async (_, { portId, data }) => {
    console.log('Write to serial port requested:', portId, data);
    return { success: false, error: 'Hardware features not available in simple mode' };
  });
}

/**
 * Create application menu
 */
function createMenu() {
  const template = [
    {
      label: 'File',
      submenu: [
        {
          label: 'Exit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// App event handlers
app.whenReady().then(() => {
  createMainWindow();
  setupIpcHandlers();
  createMenu();

  // Setup display change listeners after app is ready
  screen.on('display-added', () => {
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('displays-changed', getDisplayInfo());
    }
  });

  screen.on('display-removed', () => {
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('displays-changed', getDisplayInfo());
    }
  });

  screen.on('display-metrics-changed', () => {
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('displays-changed', getDisplayInfo());
    }
  });

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createMainWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

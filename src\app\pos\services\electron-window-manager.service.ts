import { Injectable, OnD<PERSON>roy } from '@angular/core';
import { Observable, BehaviorSubject, Subject } from 'rxjs';
import { ElectronService, ElectronDisplay, StudentWindowOptions, ActiveStudentWindow } from './electron.service';

/**
 * Interface for window management preferences
 */
export interface WindowManagementPreferences {
  autoOpenStudentView: boolean;
  preferredStudentDisplay?: number;
  studentWindowSize: {
    width: number;
    height: number;
  };
  rememberWindowPositions: boolean;
  enableHardwareAcceleration: boolean;
}

/**
 * Interface for window state
 */
export interface WindowState {
  merchantWindow: {
    isActive: boolean;
    bounds?: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
  };
  studentWindows: ActiveStudentWindow[];
  totalWindows: number;
}

/**
 * Service for managing Electron-based multi-window functionality
 */
@Injectable({
  providedIn: 'root'
})
export class ElectronWindowManagerService implements OnDestroy {
  private windowStateSubject = new BehaviorSubject<WindowState>({
    merchantWindow: { isActive: true },
    studentWindows: [],
    totalWindows: 1
  });

  private preferencesSubject = new BehaviorSubject<WindowManagementPreferences>({
    autoOpenStudentView: false,
    studentWindowSize: { width: 1024, height: 768 },
    rememberWindowPositions: true,
    enableHardwareAcceleration: true
  });

  private windowEventsSubject = new Subject<{
    type: 'opened' | 'closed' | 'moved' | 'resized';
    windowId?: string;
    data?: any;
  }>();

  private isElectronApp = false;
  private activeStudentWindows: ActiveStudentWindow[] = [];

  constructor(private electronService: ElectronService) {
    this.isElectronApp = this.electronService.isElectron();
    if (this.isElectronApp) {
      this.initializeElectronWindowManagement();
      this.loadPreferences();
    }
  }

  /**
   * Initialize Electron window management
   */
  private initializeElectronWindowManagement(): void {
    // Listen for display changes
    this.electronService.getDisplays().subscribe(displays => {
      this.handleDisplaysChanged(displays);
    });

    // Setup periodic window state updates
    setInterval(() => {
      this.updateWindowState();
    }, 2000);
  }

  /**
   * Handle display configuration changes
   */
  private handleDisplaysChanged(displays: ElectronDisplay[]): void {
    console.log('Displays changed:', displays);
    
    // Check if any student windows are on disconnected displays
    this.activeStudentWindows.forEach(async (window) => {
      const displayExists = displays.find(d => d.id === window.bounds.x);
      if (!displayExists) {
        console.log(`Display for window ${window.windowId} no longer exists, closing window`);
        await this.closeStudentWindow(window.windowId);
      }
    });
  }

  /**
   * Update window state
   */
  private async updateWindowState(): Promise<void> {
    if (!this.isElectronApp) return;

    try {
      const activeWindows = await this.electronService.getActiveStudentWindows();
      this.activeStudentWindows = activeWindows;

      const windowState: WindowState = {
        merchantWindow: { isActive: true },
        studentWindows: activeWindows,
        totalWindows: 1 + activeWindows.length
      };

      this.windowStateSubject.next(windowState);
    } catch (error) {
      console.error('Failed to update window state:', error);
    }
  }

  /**
   * Open student window on specific display
   */
  async openStudentWindow(displayId: number, options?: StudentWindowOptions): Promise<{ success: boolean; windowId?: string; error?: string }> {
    if (!this.isElectronApp) {
      return { success: false, error: 'Not running in Electron' };
    }

    try {
      const displays = this.electronService.getDisplaysSync();
      const targetDisplay = displays.find(d => d.id === displayId);
      
      if (!targetDisplay) {
        return { success: false, error: 'Target display not found' };
      }

      const preferences = this.preferencesSubject.value;
      const windowOptions: StudentWindowOptions = {
        width: preferences.studentWindowSize.width,
        height: preferences.studentWindowSize.height,
        ...options
      };

      // Calculate optimal position if not specified
      if (!windowOptions.x || !windowOptions.y) {
        const position = this.electronService.calculateOptimalPosition(
          targetDisplay,
          { width: windowOptions.width!, height: windowOptions.height! }
        );
        windowOptions.x = position.x;
        windowOptions.y = position.y;
      }

      const result = await this.electronService.openStudentWindow(displayId, windowOptions);
      
      if (result.success) {
        this.windowEventsSubject.next({
          type: 'opened',
          windowId: result.windowId,
          data: { displayId, options: windowOptions }
        });
        
        // Update window state
        await this.updateWindowState();
      }

      return result;
    } catch (error) {
      console.error('Failed to open student window:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Close specific student window
   */
  async closeStudentWindow(windowId: string): Promise<{ success: boolean; error?: string }> {
    if (!this.isElectronApp) {
      return { success: false, error: 'Not running in Electron' };
    }

    try {
      const result = await this.electronService.closeStudentWindow(windowId);
      
      if (result.success) {
        this.windowEventsSubject.next({
          type: 'closed',
          windowId,
          data: {}
        });
        
        // Update window state
        await this.updateWindowState();
      }

      return result;
    } catch (error) {
      console.error('Failed to close student window:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Close all student windows
   */
  async closeAllStudentWindows(): Promise<{ success: boolean; closedCount?: number; error?: string }> {
    if (!this.isElectronApp) {
      return { success: false, error: 'Not running in Electron' };
    }

    try {
      const result = await this.electronService.closeAllStudentWindows();
      
      if (result.success) {
        this.windowEventsSubject.next({
          type: 'closed',
          data: { closedCount: result.closedCount }
        });
        
        // Update window state
        await this.updateWindowState();
      }

      return result;
    } catch (error) {
      console.error('Failed to close all student windows:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Auto-open student window on preferred display
   */
  async autoOpenStudentWindow(): Promise<void> {
    const preferences = this.preferencesSubject.value;
    
    if (!preferences.autoOpenStudentView) return;

    const displays = this.electronService.getDisplaysSync();
    const secondaryDisplays = displays.filter(d => !d.isPrimary);
    
    if (secondaryDisplays.length === 0) return;

    const targetDisplay = preferences.preferredStudentDisplay 
      ? displays.find(d => d.id === preferences.preferredStudentDisplay)
      : secondaryDisplays[0];

    if (targetDisplay) {
      await this.openStudentWindow(targetDisplay.id);
    }
  }

  /**
   * Update window management preferences
   */
  updatePreferences(preferences: Partial<WindowManagementPreferences>): void {
    const currentPreferences = this.preferencesSubject.value;
    const updatedPreferences = { ...currentPreferences, ...preferences };
    
    this.preferencesSubject.next(updatedPreferences);
    this.savePreferences(updatedPreferences);
  }

  /**
   * Load preferences from storage
   */
  private loadPreferences(): void {
    try {
      const stored = localStorage.getItem('electron-window-preferences');
      if (stored) {
        const preferences = JSON.parse(stored);
        this.preferencesSubject.next({ ...this.preferencesSubject.value, ...preferences });
      }
    } catch (error) {
      console.error('Failed to load window preferences:', error);
    }
  }

  /**
   * Save preferences to storage
   */
  private savePreferences(preferences: WindowManagementPreferences): void {
    try {
      localStorage.setItem('electron-window-preferences', JSON.stringify(preferences));
    } catch (error) {
      console.error('Failed to save window preferences:', error);
    }
  }

  /**
   * Get window state observable
   */
  getWindowState(): Observable<WindowState> {
    return this.windowStateSubject.asObservable();
  }

  /**
   * Get preferences observable
   */
  getPreferences(): Observable<WindowManagementPreferences> {
    return this.preferencesSubject.asObservable();
  }

  /**
   * Get window events observable
   */
  getWindowEvents(): Observable<{ type: 'opened' | 'closed' | 'moved' | 'resized'; windowId?: string; data?: any }> {
    return this.windowEventsSubject.asObservable();
  }

  /**
   * Get current window state synchronously
   */
  getWindowStateSync(): WindowState {
    return this.windowStateSubject.value;
  }

  /**
   * Get current preferences synchronously
   */
  getPreferencesSync(): WindowManagementPreferences {
    return this.preferencesSubject.value;
  }

  /**
   * Check if running in Electron
   */
  isElectron(): boolean {
    return this.isElectronApp;
  }

  /**
   * Cleanup resources
   */
  ngOnDestroy(): void {
    // Close all student windows on destroy
    if (this.isElectronApp) {
      this.closeAllStudentWindows();
    }
  }
}
